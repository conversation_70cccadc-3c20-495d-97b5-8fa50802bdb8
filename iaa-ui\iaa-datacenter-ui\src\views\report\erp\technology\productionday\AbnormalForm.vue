<template>
  <div class='dialogContainer' v-show="dialogVisible" v-resizer>
    <Dialog :title="dialogTitle" v-model="dialogVisible" :width="mobile ? '100%' : '80%'" fullscreen v-dialogDrag>
      <el-form ref="formRef" :model="bodyData" :rules="formRules" label-width="120px">
        <el-card class="mb-4"  shadow="never">
          <div v-if="mobile">
              <el-form class="mobile-body-form">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="人数">
                      <el-input
                      v-model="bodyData.abnormalNum" type="number" placeholder="人数" min="0"
                        @input="(val) => bodyData.abnormalNum = Number(val)" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="工时">
                     <el-input
                      v-model="bodyData.abnormalWork " placeholder="工时" type="number" min="0" 
                      @input="(val) => bodyData.abnormalWork = Number(val)"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="问题点">
                      <el-input v-model="bodyData.abnormalRemark" placeholder="问题点" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="对策">
                      <el-input v-model="bodyData.abnormalCountermeasures" placeholder="对策" />
                    </el-form-item>
                  </el-col>

                </el-row>
              </el-form>
          </div>
          <el-table 
          v-else 
          :data="abnormalData" 
          border 
          stripe 
          scrollbar-always-on
             ref="tableRef" style="min-height: 250px;height:calc(100vh - 455px)">
            ">
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="时间段" min-width="70">
              <template #default="{ row }">
                {{ row.productionTimeStart }}-{{ row.productionTimeEnd }}
              </template>
            </el-table-column>
            <el-table-column label="工单号" min-width="100" prop="productionOrderCode">
              <template #default="{ row }">
                {{ row.productionOrderCode }}
              </template>
            </el-table-column>
            <el-table-column label="销售订单号" min-width="100" prop="salesOrderCode">
              <template #default="{ row }">
                {{ row.salesOrderCode }}
              </template>
            </el-table-column>
            <el-table-column label="品号" min-width="80" prop="productNo">
              <template #default="{ row }">
                {{ row.productNo }}
              </template>
            </el-table-column>
            <el-table-column label="机型/颜色（品名）" min-width="120" prop="modelsOrColor">
              <template #default="{ row }">
                {{ row.modelsOrColor }}
              </template>
            </el-table-column>
            <el-table-column label="数量" min-width="60" prop="hoursReportNum">
              <template #default="{ row }">
                {{ row.hoursReportNum }}
              </template>
            </el-table-column>
            <el-table-column label="单位" min-width="30" prop="units">
              <template #default="{ row }">
                {{ row.units }}
              </template>
            </el-table-column>
            <el-table-column label="问题点" min-width="120" prop="abnormalRemark">
              <template #default="{ row }">
                <el-input v-model="row.abnormalRemark" style="height: 100%;" type="textarea"  />
              </template>
            </el-table-column>

              <el-table-column label="临时对策" min-width="120" prop="abnormalCountermeasures">
              <template #default="{ row }">
                <el-input v-model="row.abnormalCountermeasures" type="textarea"  />
              </template>
            </el-table-column>
            <el-table-column label="人数" min-width="50" prop="abnormalNum">
              <template #default="{ row }">
                <el-input v-model="row.abnormalNum" type="number" class="no-spin-input" />
              </template>
            </el-table-column>
            <el-table-column label="总工时" min-width="50" prop="abnormalWork">
              <template #default="{ row }">
                <el-input v-model="row.abnormalWork" type="number" class="no-spin-input" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="{ $index }">
                <el-button type="danger" link @click="removeBodyRow($index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-form>
      <template #footer>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </Dialog>
  </div>

</template>

<script setup lang="ts">
import { ref,computed, watch } from "vue";
import { useAppStore } from '@/store/modules/app'
import { DayApi, AbnormalDayVO } from '@/api/report/technology/production'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)

const formRules = reactive({
  number: [{ required: true, message: '请输入人数', trigger: 'blur' }],
  abnormalFormTimeStart: [{ required: true, message: '请选择起始时间', trigger: 'change' }],
  abnormalFormTimeEnd: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

const bodyData = ref({
  id: 0,
  abnormalNum: 0, //异常人数
  abnormalWork: 0,//异常工时
  abnormalRemark: '',//异常问题点
  abnormalCountermeasures: ''//异常对策
})


// 表身数据（其他字段）
const abnormalData = ref<any[]>([])

// 删除行时清理监听
const removeBodyRow = (index: number) => {
  //最后一行不允许删除
  if (abnormalData.value.length === 1) {
    message.error('最后一行不允许删除！')
    return
  }
  // 删除行
  abnormalData.value.splice(index, 1)
}
/** 打开弹窗 */
const open = async (row: any,index: number, title?: any) => {
  dialogVisible.value = true
  dialogTitle.value = title
  console.log(row)
  bodyData.value.abnormalNum = row.abnormalNum
  bodyData.value.abnormalWork = row.abnormalWork
  bodyData.value.abnormalRemark = row.abnormalRemark
  bodyData.value.abnormalCountermeasures = row.abnormalCountermeasures
  bodyData.value.id=index
}
const openForm = async (row: any, title?: any) => { 
  dialogVisible.value = true
  dialogTitle.value = title
  console.log(row.value)
  abnormalData.value=row.value
}
defineExpose({ open,openForm }) // 提供 open 方法，用于打开弹窗


const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调


/** 提交表单 */
const submitForm = async () => {
  // 提交请求
  formLoading.value = true
  try {
      formLoading.value = false
      // 
      //关闭弹窗
      dialogVisible.value = false
      message.success('操作成功')
      emit('success', bodyData.value)
  } finally {
    formLoading.value = false
  }

}
</script>

<style lang="scss" scoped>
.time-range-row {
  display: flex;
  align-items: center;

  .time-select {
    margin: 0 4px;
  }

  .time-range-separator {
    margin: 0 6px;
    font-size: 16px;
    color: #606266;
    line-height: 32px;
  }
}

.card-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  text-align: center;
}

@media (max-width: 768px) {
  .mobile-body-form :deep(.el-form-item__label) {
    text-align: right !important;
    width: 80px !important;
    display: inline-block;
  }
}

:deep(.no-spin-input) {

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
    /* Firefox */
  }
}
</style>