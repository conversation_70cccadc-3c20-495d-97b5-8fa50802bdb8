<template>
  <div class='dialogContainer' v-show="dialogVisible" v-resizer>
    <Dialog :title="dialogTitle" v-model="dialogVisible" :width="mobile ? '100%' : '50%'" fullscreen v-dialogDrag>
      <div  class='my-tabs'>
    <el-tabs v-model="activeName" class="demo-tabs" :scrollable="true">
      <el-tab-pane label="正式工时" name="first">
        <TimeRangeWorkHours v-model="tabData" :type="1" />
      </el-tab-pane>
      <el-tab-pane label="临时工时" name="second">
        <TimeRangeWorkHours v-model="tabData2" :type="2" />
      </el-tab-pane>
      <el-tab-pane label="请假工时" name="third" >
        <TimeRangeWorkHours v-model="tabData3" :type="3" />
      </el-tab-pane>
      <el-tab-pane label="借出工时" name="fourth">
        <TimeRangeWorkHours v-model="tabData4" :type="4" />
      </el-tab-pane>
      <el-tab-pane label="借入工时" name="five">
        <TimeRangeWorkHours v-model="tabData5" :type="5" />
      </el-tab-pane>
    </el-tabs>
    </div>
      <template #footer>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="deleteAttendanceDay(updateBatchesId)" type="danger">删 除</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </Dialog>
  </div>

</template>

<script setup lang="ts">
import { ref } from "vue"
import { useI18n } from "vue-i18n"
import { useMessage } from "@/hooks/web/useMessage"
import TimeRangeWorkHours from "./TimeRangeWorkHours.vue"
import { DayApi } from '@/api/report/technology/production'
import { useAppStore } from '@/store/modules/app'

const { t } = useI18n()
const message = useMessage()
const appStore = useAppStore()
const mobile = ref(appStore.getMobile)


const tabData = ref<any[]>([])     // 正式工时数据
const tabData2 = ref<any[]>([])    // 临时工时数据
const tabData3 = ref<any[]>([])    // 请假工时数据
const tabData4 = ref<any[]>([])    // 借出工时数据
const tabData5 = ref<any[]>([])    // 借入工时数据

//标签页默认选中
const activeName = ref("first")
// 弹窗的是否展示
const dialogVisible = ref(false)
// 弹窗的标题
const dialogTitle = ref()
// 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formLoading = ref(false)
const updateBatchesId = ref<string>("")
/** 打开弹窗 */
const open = async (type: string, batchesId?: string, title?: string) => {
  dialogVisible.value = true
  dialogTitle.value = title
  updateBatchesId.value = batchesId || ""

  if (batchesId) {
    // 编辑逻辑可在此加载数据
    // 示例：从接口获取后赋值给 tabData, tabData2 等
    const res = await DayApi.getAttendanceDay(batchesId)
  // 映射 tabData
  tabData.value = res.tabData?.map(item => ({
    number: item.officiallyNumber,
    workingHours: item.officiallyWorkingHours,
    timeRange: item.officiallyTimeRange,
    timeStart: item.officiallyTimeRange?.split('-')[0] || '',
    timeEnd: item.officiallyTimeRange?.split('-')[1] || '',
    remark: item.officiallyRemark || ''
  })) || []

  tabData2.value = res.tabData2?.map(item => ({
    number: item.temporaryNumber,
    workingHours: item.temporaryWorkingHours,
    timeRange: item.temporaryTimeRange,
    timeStart: item.temporaryTimeRange?.split('-')[0] || '',
    timeEnd: item.temporaryTimeRange?.split('-')[1] || '',
    remark: item.temporaryRemark || ''
  })) || []

  tabData3.value = res.tabData3?.map(item => ({
    number: item.leaveNumber,
    workingHours: item.leaveWorkingHours,
    timeRange: item.leaveTimeRange,
    timeStart: item.leaveTimeRange?.split('-')[0] || '',
    timeEnd: item.leaveTimeRange?.split('-')[1] || '',
    remark: item.leaveRemark || ''
  })) || []

  tabData4.value = res.tabData4?.map(item => ({
    number: item.lendingNumber,
    workingHours: item.lendingWorkingHours,
    timeRange: item.lendingTimeRange,
    timeStart: item.lendingTimeRange?.split('-')[0] || '',
    timeEnd: item.lendingTimeRange?.split('-')[1] || '',
    remark: item.lendingRemark || ''
  })) || []

  tabData5.value = res.tabData5?.map(item => ({
    id: item.id,
    number: item.borrowNumber,
    workingHours: item.borrowWorkingHours,
    timeRange: item.borrowTimeRange,
    timeStart: item.borrowTimeRange?.split('-')[0] || '',
    timeEnd: item.borrowTimeRange?.split('-')[1] || '',
    remark: item.borrowRemark || ''
  })) || []
  // console.log(tabData.value)
  // console.log(tabData2.value)
  // console.log(tabData3.value)
  // console.log(tabData4.value)
  // console.log(tabData5.value)
  } else {
    resetFormData()
  }
}
defineExpose({ open }) // 对外暴露方法

// 重置表单
const resetFormData = () => {
  tabData.value = []
  tabData2.value = []
  tabData3.value = []
  tabData4.value = []
  tabData5.value = []
}

const emit = defineEmits(["success"])

/** 提交数据 */
const submitForm = async () => {
  try {
    formLoading.value = true

    const batchesId = updateBatchesId.value

     // 构建请求体
    const payload = {
      tabData: tabData.value
  .filter(data => 
    data.timeRange && 
    data.timeRange !== "-" && 
    data.number !== 0 && 
    data.workingHours !== 0
      )
        .map(data => ({
          officiallyNumber: data.number || 0,
          officiallyWorkingHours: data.workingHours || 0,
          officiallyTimeRange: data.timeRange || "",
          officiallyRemark: data.remark || "",
          batchesId
        })),

      tabData2: tabData2.value
  .filter(data => 
    data.timeRange && 
    data.timeRange !== "-" && 
    data.number !== 0 && 
    data.workingHours !== 0
      )
        .map(data => ({
          temporaryNumber: data.number || 0,
          temporaryWorkingHours: data.workingHours || 0,
          temporaryTimeRange: data.timeRange || "",
          temporaryRemark: data.remark || "",
          batchesId
        })),

      tabData3: tabData3.value
  .filter(data => 
    data.timeRange && 
    data.timeRange !== "-" && 
    data.number !== 0 && 
    data.workingHours !== 0
      )
        .map(data => ({
          leaveNumber: data.number || 0,
          leaveWorkingHours: data.workingHours || 0,
          leaveTimeRange: data.timeRange || "",
          leaveRemark: data.remark || "",
          batchesId
        })),

      tabData4: tabData4.value
  .filter(data => 
    data.timeRange && 
    data.timeRange !== "-" && 
    data.number !== 0 && 
    data.workingHours !== 0
      )
        .map(data => ({
          lendingNumber: data.number || 0,
          lendingWorkingHours: data.workingHours || 0,
          lendingTimeRange: data.timeRange || "",
          lendingRemark: data.remark || "",
          batchesId
        })),

      tabData5: tabData5.value
  .filter(data => 
    data.timeRange && 
    data.timeRange !== "-" && 
    data.number !== 0 && 
    data.workingHours !== 0
      )
        .map(data => ({
          borrowNumber: data.number || 0,
          borrowWorkingHours: data.workingHours || 0,
          borrowTimeRange: data.timeRange || "",
          borrowRemark: data.remark || "",
          batchesId
        })),
        batchesId
    }

    // 如果所有数组都为空，并且是编辑状态，则删除整条记录
    if(payload.tabData.length === 0 &&
      payload.tabData2.length === 0 &&
      payload.tabData3.length === 0 &&
      payload.tabData4.length === 0 &&
      payload.tabData5.length === 0){
        message.warning("请至少填写一项出勤数据")
    }else{ 
      // 调用后端接口，一次性提交全部数据
      const res= await DayApi.createAttendance(payload)
      message.success("操作成功")
      dialogVisible.value = false
      emit("success", res)
    }


   } finally {
    formLoading.value = false
  }
}

/** 删除数据 */
const deleteAttendanceDay = async (batchesId: string) => {
  await message.confirm(`确定要删除所有出勤工时吗？`)
  formLoading.value = true
  try {
    await DayApi.deleteAttendanceDay(batchesId)
    //关闭弹窗
    dialogVisible.value = false
    // 重置表单数据
    resetFormData()
    const res: any = {
      batchesId: updateBatchesId.value,
      totalNumberWork: 0
    }

    emit('success', res)
    message.success('删除成功')

  } finally {
    formLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
@media(max-width:768px) {
  :deep(.el-form-item__label) {
    width: 60px !important;
    /* 覆盖内联样式的宽度 */
  }
  :deep(.el-dialog__body){
    padding: 5px !important;
  }
}

.my-tabs  :deep(.el-tabs__item.is-active) {
  background: rgba(0, 102, 255, 0.08);
  border-radius: 4px 4px 3px 3px;
}
 
.my-tabs  :deep(.el-tabs__active-bar) {
  background-color: #166fe8; /* 修改底部横杠的颜色 */
  height: 3px; /* 修改底部横杠的高度 */
  // width: 100px !important;
  // left: 30%;
}
.my-tabs :deep(.el-tabs__active-bar::after){
//给激活的时底部tab横线添加三角
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  left: 39%;
  bottom:2px;
  border-top: 10px solid transparent;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-bottom: 5px solid #166fe8;
}
.my-tabs :deep(.el-tabs__item) {
  //
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px !important;
  padding: 0 !important;
}
.my-tabs :deep(.el-tabs__nav.is-top){
  display: flex;
}
.my-tabs :deep(.el-tabs__nav.is-top) > div {
  width: 100px;
  text-align: center;
}
</style>
