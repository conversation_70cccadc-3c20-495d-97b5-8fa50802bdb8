<template>
  <ContentWrap>
    <el-tabs size="small" v-model="currentTab">
      <el-tab-pane label="成品与工艺路线绑定" name="1" />
      <el-tab-pane label="工艺路线定义" name="2" />
    </el-tabs>
    <div class="h-[calc(100vh-180px)]">
      <RouteLink ref="routeLinkRef" v-if="currentTab === '1'" />
      <RouteDefine @search="onSearch" v-if="currentTab === '2'" />
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import RouteDefine from './RouteDefine.vue'
import RouteLink from './RouteLink.vue'

const currentTab = ref('1')
const routeLinkRef = ref()

const onSearch = async (id: number) => {
  currentTab.value = '1'
  await nextTick()
  routeLinkRef.value.setQueryParams(id)
}
</script>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.vxe-buttons--wrapper) {
  & > * + * {
    margin-left: 10px;
  }
}
</style>
