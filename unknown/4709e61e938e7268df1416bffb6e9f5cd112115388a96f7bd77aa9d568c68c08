<template>
  <el-form label-width="120" size="small">
    <el-row>
      <el-col :span="12" v-if="!['logo'].includes(props.data.type!)">
        <el-form-item label="计划资料接收日期">
          {{ props.data.planReceiptDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['logo'].includes(props.data.type!)">
        <el-form-item label="实际资料接收日期">
          {{ props.data.actualReceiptDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计划设计日期">
          {{ props.data.planDesignDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实际设计日期">
          {{ props.data.actualDesignDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program', 'structure'].includes(props.data.type!)">
        <el-form-item :label="props.data.type == 'packing' ? '计划设计稿确认' : '计划报价日期'">
          {{ props.data.planQuotationDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program', 'structure'].includes(props.data.type!)">
        <el-form-item :label="props.data.type == 'packing' ? '实际设计稿确认' : '实际报价日期'">
          {{ props.data.actualQuotationDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计划打样日期">
          {{ props.data.planTestingDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实际打样日期">
          {{ props.data.actualTestingDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program'].includes(props.data.type!)">
        <el-form-item
          :label="
            ['packing', 'instruction'].includes(props.data.type!) ? '计划确认日期' : '计划承认日期'
          "
        >
          {{ props.data.planAdmitDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program'].includes(props.data.type!)">
        <el-form-item
          :label="
            ['packing', 'instruction'].includes(props.data.type!) ? '实际确认日期' : '实际承认日期'
          "
        >
          {{ props.data.actualAdmitDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program', 'packing'].includes(props.data.type!)">
        <el-form-item label="计划变更日期">
          {{ props.data.planChangeDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program', 'packing'].includes(props.data.type!)">
        <el-form-item label="实际变更日期">
          {{ props.data.actualChangeDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program', 'structure'].includes(props.data.type!)">
        <el-form-item label="计划BOM日期">
          {{ props.data.planBomDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!['program', 'structure'].includes(props.data.type!)">
        <el-form-item label="实际Bom日期">
          {{ props.data.actualBomDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计划完成日期">
          {{ props.data.planCompleteDate }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实际完成日期">
          {{ props.data.actualCompleteDate }}
        </el-form-item>
      </el-col>
      <el-col :span="24">
        {{ props.data.remark }}
      </el-col>
    </el-row>
  </el-form>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  data: propTypes.any.isRequired
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 10px !important;
}
:deep(.el-form-item__content) {
  width: 100%;
  background-color: #f5f7f9;
  padding: 1px 5px;
}
</style>
