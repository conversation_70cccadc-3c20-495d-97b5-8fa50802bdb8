<template>
  <div class="dialogContainer" v-show="dialogVisible">
    <Dialog
:title="dialogTitle" v-model="dialogVisible" :width="'100%'" :height="'100%'" :draggable="true"
      :overflow="true" :fullscreen="true">
      <el-form ref="formRef" :model="headerData" :rules="formRules" label-width="80px" v-loading="formLoading">
        <!-- 表头信息 -->
        <el-card class="mb-4" shadow="never">
          <el-row>
            <el-col :span="4" :xs="12">
              <el-form-item label="产线" :label-width="mobile ? '' : '70px'" prop="productionLine">
                <el-select v-model="headerData.productionLine" placeholder="请选择产线">
                  <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_LINE)" :key="dict.value"
                    :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="班组长" :label-width="mobile ? '' : '58px'" prop="teamLeader">
                <el-input v-model="headerData.teamLeader" placeholder="请输入班组长" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="日期" :label-width="mobile ? '' : '36px'" prop="dateStr">
                <el-date-picker
v-model="headerData.dateStr" type="date" value-format="x" placeholder="选择日期"
                  style="width: 100%" :prefix-icon="customPrefix" class="custom-date-picker" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="应出勤" :label-width="mobile ? '' : '72px'" prop="requiredAttendanceNum">
                <el-input
v-model="headerData.requiredAttendanceNum" style="width: 100%" type="number" min="0"
                  class="no-spin-input" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="实际出勤" :label-width="mobile ? '' : '84px'" prop="actualAttendanceNum">
                <el-input
v-model="headerData.actualAttendanceNum" style="width: 100%" type="number" min="0"
                  class="no-spin-input" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="组装人数" :label-width="mobile ? '' : '72px'" prop="assembledNum">
                <el-input
v-model="headerData.assembledNum" style="width: 100%" type="number" min="0"
                  @change="syncBodyNumber" class="no-spin-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- <el-col :span="4" :xs="12">
              <el-form-item label="组装总工时" :label-width="mobile ? '' : '72px'" prop="assembledTotal">
                <el-input
v-model="headerData.assembledTotal" style="width: 100%"
                  oninput="value=value.replace(/[^0-9.]/g,'')" :readonly="true" placeholder="系统自动计算" />
              </el-form-item>
            </el-col> -->
            <el-col :span="4" :xs="12">
              <el-form-item label="工序" :label-width="mobile ? '' : '70px'" prop="type">
                <el-select v-model="headerData.type" placeholder="工序" style="width: 100%">
                  <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)" :key="dict.value"
                    :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="标准工时" :label-width="mobile ? '' : '60px'" prop="standard_work">
                <el-input
v-model="headerData.standardWork" type="number" :min="0" style="width: 100%"
                  @change="syncBodyNumber" class="no-spin-input">
                  <template #append>分</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4" :xs="24">
              <el-form-item label="备注" :label-width="mobile ? '' : '36px'" prop="remarkHead">
                <el-input v-model="headerData.remarkHead" placeholder="请输入备注" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="组装工时" :label-width="mobile ? '' : '72px'">
                <el-input
v-model="headerData.assembledTotal" type="number" placeholder="系统自动计算" :min="0"
                  style="width: 100%" :readonly="true" />
              </el-form-item>
            </el-col>
           <el-col :span="4" :xs="12">
              <el-form-item label="出勤工时" :label-width="mobile ? '' : '84px'">
                <el-input
v-model="headerData.numberWork" type="number" placeholder="系统自动计算" :min="0"
                  style="width: 100%" :readonly="true" />
              </el-form-item>
            </el-col>
            <el-col :span="2" :xs="12">
              <el-button
@click="openFormAttendance('create', headerData.batchesId, '出勤工时录入')" class="w-full" plain
                :style="mobile ? { 'margin-top': '5px' } : null">
                出勤工时录入
              </el-button>
            </el-col>
            <el-col :span="2" :xs="12" v-if="!mobile">
              <el-button
@click="openFormPC('异常工时录入')" class="w-full" plain
                :style="mobile ? { 'margin-top': '5px' } : null">
                异常工时录入
              </el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 表身信息 -->
        <el-card shadow="never" class="custom-card">
          <template #header v-if="!mobile">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <el-button type="primary" @click="addBodyRow" size="small" plain>
                <Icon icon="ep:plus" style="margin-right: 4px" /> 新增行
              </el-button>
            </div>
          </template>
          <div v-if="mobile">
            <div v-for="(row, index) in bodyData" :key="index" class="card-item">
              <div class="card-title">第 {{ index + 1 }} 条 / 共 {{ bodyData.length }} 条 <span>
                  <el-button @click.stop="addBodyRow" :icon="Plus" size="small">
                    新增行
                  </el-button>
                </span>
              </div>
              <el-form class="mobile-body-form">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="时间段">
                      <div class="time-range-row">
                        <el-time-select
v-model="row.productionTimeStart" placeholder="起始时间" :start="'08:30'"
                          :end="'23:30'" :step="'00:15'" placement="top-start" />
                        <span class="time-range-separator">-</span>
                        <el-time-select
v-model="row.productionTimeEnd" placeholder="结束时间" :start="'08:30'"
                          :end="'23:30'" :step="'00:15'" :min-time="row.productionTimeStart" placement="top-start" />
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="工单号">
                      <el-autocomplete
v-model="row.productionOrderCode" :fetch-suggestions="queryProductionOrder"
                        placeholder="输入工单号" style="width: 100%"
                        @select="(item) => handleProductionOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.productionOrderCode }}-{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="销售订单号">
                      <el-autocomplete
v-model="row.salesOrderCode" :fetch-suggestions="querySalesOrder"
                        placeholder="输入销售订单号" style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="品号">
                      <el-autocomplete
v-model="row.productNo" :fetch-suggestions="(queryString, cb) =>
                        queryProductNo(
                          queryString,
                          cb,
                          row.productionOrderCode,
                          row.salesOrderCode
                        )
                        " placeholder="输入品号查询" style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <el-input v-model="row.productNo" placeholder="品号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="工序">
                      <template #default>
                        <el-select
v-model="row.type" placeholder="请选择工序" style="width: 100%"
                          @change="(value) => handleTypeChange(value, row)">
                          <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)"
                            :key="dict.value" :label="dict.label" :value="dict.value" />
                        </el-select>
                      </template>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品名">
                      <el-input v-model="row.modelsOrColor" placeholder="机型/颜色" readonly />
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="数量">
                      <el-input v-model="row.workOrderNum" placeholder="数量" readonly />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="人数">
                      <el-input v-model="row.number" placeholder="人数" min="0" type="number" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="生产数">
                      <el-input
v-model="row.hoursReportNum" placeholder="生产数量" type="number" :min="0"
                        :max="row.workOrderNum" @input="(val) => validateHoursReportNum(val, row)" />
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="累计数">
                      <el-input
v-model="row.totalReportNum" placeholder="累计数" :readonly="true" type="number"
                        :min="0" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="备注">
                      <el-input v-model="row.remark" placeholder="备注" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="标准工时">
                      <el-input v-model="row.standardWork">
                        <template #append>分</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="row.abnormalWork">
                    <el-form-item label="异常总工时">
                      <el-input v-model="row.abnormalWork" :readonly="true" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <!-- <el-col :span="8">
                    <el-button type="primary" plain @click="addBodyRow" class="w-full">
                      新增行
                    </el-button>
                  </el-col> -->
                  <el-col :span="12">
                    <el-button plain @click="openForm(index, '异常工时录入')" class="w-full">
                      异常工时
                    </el-button>
                  </el-col>
                  <el-col :span="12">
                    <el-button plain @click="removeBodyRow(index)" class="w-full">
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <el-table
v-else :data="bodyData" border stripe scrollbar-always-on @row-contextmenu="rightClick"
            @row-click="clickTableRow" ref="tableRef" style="min-height: 250px;height:calc(100vh - 455px)">
            ">
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="时间段" min-width="200">
              <template #default="{ row }">
                <div class="time-range-row">
                  <el-time-select
v-model="row.productionTimeStart" placeholder="起始时间" :start="'08:30'" :end="'23:30'"
                    :editable="false" :step="'00:15'" placement="top-start" />
                  <span class="time-range-separator">-</span>
                  <el-time-select
v-model="row.productionTimeEnd" placeholder="结束时间" :start="'08:30'" :end="'23:30'"
                    :editable="false" :step="'00:15'" :min-time="row.productionTimeStart" placement="top-start" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="工序" min-width="80" prop="type">
              <template #default="{ row }">
                <el-select
v-model="row.type" placeholder="请选择工序" style="width: 100%"
                  @change="(value) => handleTypeChange(value, row)">
                  <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)" :key="dict.value"
                    :label="dict.label" :value="dict.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="工单号" min-width="160" prop="productionOrderCode">
              <template #default="{ row, $index }">
                <el-autocomplete
v-model="row.productionOrderCode" :fetch-suggestions="queryProductionOrder"
                  placeholder="输入工单号" style="width: 100%" @select="(item) => handleProductionOrderSelect(item, $index)"
                  :trigger-on-focus="false" :debounce="300" popper-class="production-order-autocomplete"
                  placement="top-start">
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text">{{ item.productionOrderCode }}-{{ item.productNo }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column label="销售订单号" min-width="160" prop="salesOrderCode">
              <template #default="{ row, $index }">
                <el-autocomplete
v-model="row.salesOrderCode" :fetch-suggestions="querySalesOrder" placeholder="输入销售订单号"
                  style="width: 100%" @select="(item) => handleSalesOrderSelect(item, $index)" :trigger-on-focus="false"
                  :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column label="品号" min-width="110" prop="productNo">
              <template #default="{ row, $index }">
                <el-autocomplete
v-model="row.productNo" :fetch-suggestions="(queryString, cb) =>
                  queryProductNo(queryString, cb, row.productionOrderCode, row.salesOrderCode)
                  " placeholder="输入品号查询" style="width: 100%" @select="(item) => handleSalesOrderSelect(item, $index)"
                  :trigger-on-focus="false" :debounce="300" popper-class="production-order-autocomplete"
                  placement="top-start">
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text">{{ item.productNo }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column label="机型/颜色（品名）" min-width="120" prop="modelsOrColor">
              <template #default="{ row }">
                <el-input v-model="row.modelsOrColor" placeholder="机型/颜色" readonly />
              </template>
            </el-table-column>
            <el-table-column label="工单数量" min-width="70" prop="workOrderNum">
              <template #default="{ row }">
                <el-input v-model="row.workOrderNum" placeholder="工单数量" readonly />
              </template>
            </el-table-column>
            <el-table-column label="单位" min-width="50" prop="units">
              <template #default="{ row }">
                <el-input v-model="row.units" placeholder="单位" readonly />
              </template>
            </el-table-column>
            <el-table-column label="标准工时" min-width="70" prop="standardWork">
              <template #default="{ row, $index}">
                <el-input
                :ref="(el) => addNavigableInput(el, $index, 8)"
     @keydown="(e) => handleKeyDown($index, 8, e)"
                  oninput="value=value.replace(/^\.+|[^\d.]/g,'')" v-model="row.standardWork" placeholder="标准工时"
                  type="number" class="no-spin-input" />
              </template>
            </el-table-column>
            <el-table-column label="人数" min-width="70" prop="number">
              <template #default="{ row, $index }">
                <el-input
                :ref="(el) => addNavigableInput(el, $index, 9)"
      @keydown="(e) => handleKeyDown($index, 9, e)" v-model="row.number" placeholder="人数"
                  type="number" class="no-spin-input" />
              </template>
            </el-table-column>
            <el-table-column label="生产数" min-width="60" prop="hoursReportNum">
              <template #default="{ row, $index }">
                <el-input
                :ref="(el) => addNavigableInput(el, $index, 10)"
      @keydown="(e) => handleKeyDown($index, 10, e)" v-model="row.hoursReportNum" placeholder="生产数"
                  type="number" :min="0" :max="row.workOrderNum" class="no-spin-input"
                  @input="(val) => validateHoursReportNum(val, row)" />
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="120" prop="remark">
              <template #default="{ row, $index }">
                <el-input
                :ref="(el) => addNavigableInput(el, $index, 11)"
      @keydown="(e) => handleKeyDown($index, 11, e)" v-model="row.remark" placeholder="备注" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="{ $index }">
                <el-button type="danger" link @click="removeBodyRow($index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 右键菜单 -->
          <div id="menu" class="menuDiv" style="position: fixed;">
            <ul class="menuUl">
              <li v-for="(item, index) in menus" :key="index" @click.stop="infoClick(index)">
                {{ item.name }}
              </li>
            </ul>
          </div>
        </el-card>
      </el-form>
      <template #footer>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </Dialog>
  </div>
  <AbnormalForm ref="formRefAbnormal" @success="handleAbnormalSuccess" />
  <AttendanceForm ref="formRefAttendance" @success="handleAttendanceSuccess" />
</template>
<script setup lang="ts">
import { DayApi, DayVO } from '@/api/report/technology/production'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { dateUtil } from '@/utils/dateUtil'
import { ref, DirectiveBinding } from 'vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import AbnormalForm from './AbnormalForm.vue'
import AttendanceForm from './AttendanceForm.vue'
import _ from 'lodash'
import { Plus } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash-es'
import { type } from '../../../../../types/auto-imports';

/** 生产日报 表单 */
defineOptions({ name: 'DayForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)

//方向键上下左右切换单元格
const tableRef = ref() // 添加表格引用
const focusedCell = ref({ rowIndex: -1, columnIndex: -1 })
//键盘处理逻辑
    
    // 键盘导航功能
    const navigableInputRefs = ref<Map<string, HTMLInputElement>>(new Map());

    // 添加输入框引用
    const addNavigableInput = (el: any, rowIndex: number, colIndex: number) => {
      if (!el) return;

      let inputElement: HTMLInputElement | null = null;

      // 处理不同类型的元素引用
      if (el instanceof HTMLInputElement) {
        inputElement = el;
      } else if (el.$el && el.$el.querySelector) {
        inputElement = el.$el.querySelector('input');
      } else if (el.querySelector) {
        inputElement = el.querySelector('input');
      }

      if (inputElement) {
        const key = `${rowIndex}-${colIndex}`;
        navigableInputRefs.value.set(key, inputElement);
        // console.log(`添加输入框引用: ${key}`, inputElement);
      }
    };

    // 获取输入框引用
    const getInputElement = (rowIndex: number, colIndex: number): HTMLInputElement | null => {
      const key = `${rowIndex}-${colIndex}`;
      return navigableInputRefs.value.get(key) || null;
    };

    // 重新收集所有输入框引用
    const refreshNavigableInputs = () => {
      console.log('开始重新收集输入框引用...');
      navigableInputRefs.value.clear();

      // 等待DOM更新后重新收集
      nextTick(() => {
        // 查找所有可导航的输入框
        const tableElement = document.querySelector('.el-table__body');
        if (tableElement) {
          const rows = tableElement.querySelectorAll('tr');
          rows.forEach((row, rowIndex) => {
            // 查找第8-15列的输入框（标准工时到异常对策）
            for (let colIndex = 8; colIndex <= 15; colIndex++) {
              const cellSelector = `td:nth-child(${colIndex + 2})`; // colIndex 8对应第10列（标准工时）
              const cell = row.querySelector(cellSelector);
              if (cell) {
                const input = cell.querySelector('input');
                if (input) {
                  const key = `${rowIndex}-${colIndex}`;
                  navigableInputRefs.value.set(key, input);
                }
              }
            }
          });
        }
      });
    };

    // 方向键导航处理
    const handleKeyDown = (rowIndex: number, colIndex: number, event: KeyboardEvent) => {
      const key = event.key;

      // 只处理方向键
      if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
        return;
      }

      // 防止页面滚动
      event.preventDefault();

      // 定义导航范围
      const START_COL_INDEX = 8; // 标准工时列的索引
      const END_COL_INDEX = 15;   // 异常对策列的索引
      const totalRows = bodyData.value.length;

      let nextRow = rowIndex;
      let nextCol = colIndex;

      // 根据方向键计算下一个位置
      switch (key) {
        case 'ArrowUp':
          nextRow = Math.max(0, rowIndex - 1);
          break;
        case 'ArrowDown':
          nextRow = Math.min(totalRows - 1, rowIndex + 1);
          break;
        case 'ArrowLeft':
          nextCol = Math.max(START_COL_INDEX, colIndex - 1);
          break;
        case 'ArrowRight':
          nextCol = Math.min(END_COL_INDEX, colIndex + 1);
          break;
      }


      // 查找目标输入框并聚焦
      const targetInput = getInputElement(nextRow, nextCol);
      if (targetInput) {
        console.log('找到目标输入框，正在聚焦...');
        setTimeout(() => {
          targetInput.focus();
          targetInput.select();
        }, 0);
      } else {
        console.log(`未找到目标输入框: (${nextRow}, ${nextCol})`);
      }
    };




const menus = [
  { name: '列批量赋值', operType: 1 },
  { name: '生成剩余生产数', operType: 2 },
]

const currentRow = ref<any>(null);
const currentColumn = ref<any>(null);
const rightClick = (row, column, event) => {
  currentRow.value = row; // 保存当前行的信息
  currentColumn.value = column; // 保存当前列的信息
  const menu = document.getElementById('menu');
  if (!menu) return;

  event.preventDefault();


  menu.style.left = `${event.clientX + 10}px`;
  menu.style.top = `${event.clientY - 310}px`;
  menu.style.display = 'block';
  menu.style.zIndex = '1000';
};

//table的左键点击当前行事件
const clickTableRow = (row, column, event) => {
  const menu = document.getElementById('menu') as HTMLElement;
  menu.style.display = "none";
}

//自定义菜单的点击事件
const infoClick = async (index)  => {
  if (index === 0) {
    if (!currentRow.value || !currentColumn.value) {
      message.error('请先右键选择一个单元格');
      return;
    }
    const fieldName = currentColumn.value.property; // 获取列对应的字段名，如 'productionOrderCode'
    if (!fieldName || fieldName === 'productionOrderCode' || fieldName === 'salesOrderCode'
      || fieldName === 'modelsOrColor' || fieldName === 'productNo' || fieldName === 'workOrderNum'
      || fieldName === 'units'
    ) {
      message.error('该列无法批量填写');
      const menu = document.getElementById('menu') as HTMLElement;
      menu.style.display = "none";
      return;
    }
    const fieldValue = currentRow.value[fieldName]; // 获取当前行该字段的值
    // 遍历 bodyData 所有行，批量赋值
    bodyData.value.forEach(row => {
      row[fieldName] = fieldValue;
    });
    message.success(`操作成功`);
  } else if (index === 1) {
    if (formType.value === 'update') {
      message.error(`该操作仅限新增数据时使用`);
    } else {
      for (let i = 0; i < bodyData.value.length; i++) {
        const row = bodyData.value[i];
        if (!row.productionOrderCode && !row.salesOrderCode) {
          message.error(`第${i + 1}行：工单号或销售订单号不能为空，请补充！`)
          const menu = document.getElementById('menu') as HTMLElement;
          menu.style.display = "none";
          return false
        }
        if (!row.productNo) {
          message.error(`第${i + 1}行：品号不能为空，请补充！`)
          const menu = document.getElementById('menu') as HTMLElement;
          menu.style.display = "none";
          return false
        }
      }

      //生成剩余生产数
      const res=await DayApi.generated(bodyData.value);
      bodyData.value.map((row, i) => {
        row.hoursReportNum = res[i]
      })
    }
  }
  const menu = document.getElementById('menu') as HTMLElement;
  menu.style.display = "none";
}
// 查询生产工单号
const queryProductionOrder = (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }

  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ productionOrderCode: '加载中...', loading: true }])

  DayApi.getWorkSalesOptions(queryString, 0) // type=0 表示生产工单号查询
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ productionOrderCode: '暂无数据', noData: true }])
    })
    .catch(() => {
      cb([{ productionOrderCode: '查询失败', error: true }])
    })
}

//异常工时录入、出勤录入
const formRefAbnormal = ref()
const formRefAttendance = ref()
const openForm = (index: number, title?: any) => {
  formRefAbnormal.value.open(bodyData.value[index], index, title)
}

const openFormPC = (title?: any) => {
    // 使用 lodash 的 cloneDeep 实现深拷贝
  bodyAbnormalData.value = cloneDeep(bodyData.value)
  formRefAbnormal.value.openForm(bodyAbnormalData, title)
}


const openFormAttendance = (type: string, batchesId?: any, title?: any) => {
  formRefAttendance.value.open(type, batchesId, title)
}


// 查询销售订单号
const querySalesOrder = (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }

  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ salesOrderCode: '加载中...', loading: true }])

  DayApi.getWorkSalesOptions(queryString, 1)
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ salesOrderCode: '暂无数据', noData: true }])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([{ salesOrderCode: '查询失败', error: true }])
    })
}
// 查询品号
const queryProductNo = (
  queryString: string,
  cb: Function,
  productionOrderCode: string,
  salesOrderCode: string
) => {
  if (!queryString) {
    cb([])
    return
  }
  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ productNo: '加载中...', loading: true }])
  //把工单号和销售订单号和输入的品号都传给后端
  const data = {
    productionOrderCode: productionOrderCode, // 生产工单号
    salesOrderCode: salesOrderCode, // 销售订单号
    productNo: queryString // 输入的品号
  }
  DayApi.getProductNo(data)
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ productNo: '暂无数据', noData: true }])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([{ productNo: '查询失败', error: true }])
    })
}
// 选择生产工单号后的处理
const handleProductionOrderSelect = async (item: any, rowIndex: number) => {
  if (item.loading || item.noData || item.error) return

  const row = bodyData.value[rowIndex]
  if (row && item) {
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.workOrderNum = item.workOrderNum
    row.units = item.units

    const dayVO: any = {
      productionOrderCode: item.productionOrderCode,
      salesOrderCode: item.salesOrderCode,
      type: row.type,
      dateStr: headerData.value.dateStr
    }

    const data = await DayApi.getOrderCodeNum(dayVO)
    const key = `${row.productionOrderCode}-${row.type}`

    // 设置初始累计值
    rowTotalReportNums.value[key] = data || 0
    row.totalReportNum = data || 0

    // 如果该订单已有生产数，则用已有的生产数作为原始值
    originalReportNums.value[key] = row.hoursReportNum || 0
  }
}
const customPrefix = shallowRef({
  render() {
    return null
  }
})
// 选择销售订单号后的处理
const handleSalesOrderSelect = async (item: any, rowIndex: number) => {
  // 忽略加载状态、无数据状态、错误状态的选择
  if (item.loading || item.noData || item.error) {
    return
  }

  const row = bodyData.value[rowIndex]
  if (row && item) {
    // 设置生产工单号
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.workOrderNum = item.workOrderNum
    row.units = item.units
    handleTypeChange(row.type, row)
  }
}

//设置异常人数时把当前表格所有相同时间段和工序的数据全部赋值一样的异常人数
const handleAbnormalNumChange = (rowIndex: number) => {
  const currentRow = bodyData.value[rowIndex];
  if (!currentRow) return;

  // 构造当前时间段标识符
  const timeRange = `${currentRow.productionTimeStart}-${currentRow.productionTimeEnd}`;

  // 遍历整个 bodyData 同步相同时间段 + 工序的异常人数
  bodyData.value.forEach((row) => {
    const rowTimeRange = `${row.productionTimeStart}-${row.productionTimeEnd}`;
    if (row.type === currentRow.type && rowTimeRange === timeRange) {
      row.abnormalNum = currentRow.abnormalNum;
      row.abnormalWork = currentRow.abnormalWork;
    }
  });
};

//修改的时候触发
const updateAbnormalNumChange = async (rowIndex: number) => {
  if (formType.value !== 'update') return;
  // alert('修改了异常数量')
  const currentRow = bodyData.value[rowIndex]

  if (!currentRow) return

  const { productionTimeStart, productionTimeEnd, type, abnormalNum, abnormalWork } = currentRow
  const timeRange = `${productionTimeStart}-${productionTimeEnd}`

  // 收集需要更新的数据
  const updateList = bodyData.value
    .filter(row => {
      const rowTimeRange = `${row.productionTimeStart}-${row.productionTimeEnd}`
      return row.id && row.type === type && rowTimeRange === timeRange
    })
    .map(row => ({
      id: row.id,
      abnormalNum: isNaN(parseInt(abnormalNum)) ? 0 : parseInt(abnormalNum),
      abnormalWork: abnormalWork ? parseFloat(abnormalWork) : 0,
    }))

  // 如果没有需要更新的数据，直接返回
  if (updateList.length === 0) return

  try {
    // 调用后端接口一次性更新所有匹配行
    await DayApi.batchUpdateAbnormalNum(updateList)
    message.success('异常人数同步成功')
  } catch (error) {
    message.error('同步失败，请重试')
    console.error('同步异常人数失败:', error)
  }
};
// 切换工序时查询累计数
const handleTypeChange = async (value: number, row: any) => {
  // 新增：主动触发校验
  if (!row.productionOrderCode) return

  const dayVO: any = {
    productionOrderCode: row.productionOrderCode,
    salesOrderCode: row.salesOrderCode,
    type: value,
    dateStr: headerData.value.dateStr
  }

  const data = await DayApi.getOrderCodeNum(dayVO)
  const key = `${row.productionOrderCode}-${value}`

  row.type = value
  rowTotalReportNums.value[key] = data || 0
  row.totalReportNum = data || 0

  // 如果该订单已有生产数，则用已有的生产数作为原始值
  originalReportNums.value[key] = row.hoursReportNum || 0


}

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
// const dialogSizeMap = new Map<string, { width: string; height: string }>()

const totalReportNum = ref(0)
const headerData = ref({
  productionLine: 1,
  teamLeader: '',
  dateStr: '',
  requiredAttendanceNum: undefined,
  actualAttendanceNum: undefined,
  assembledNum: undefined,
  assembledTotal: undefined,
  standardWork: undefined,
  remarkHead: undefined,
  batchesId: undefined,
  numberWork: undefined,
  type: 1
})

// 表身数据（其他字段）
const bodyData = ref<any[]>([])


//异常工时表身数据
const bodyAbnormalData = ref<any[]>([])
// 创建空的表身行
const createEmptyBodyRow = (lastEndTime = '', lastStrTime = '', number = 0, standardWork = 0, type = 1) => ({
  productionTimeStart: lastStrTime,
  productionTimeEnd: lastEndTime,
  type: type,
  productionOrderCode: undefined,
  salesOrderCode: undefined,
  productNo: undefined,
  modelsOrColor: undefined,
  workOrderNum: undefined,
  units: undefined,
  number: number,
  hoursReportNum: undefined,
  totalReportNum: undefined,
  standardWork: standardWork,
  abnormalWork: undefined,
  abnormalNum: undefined,
  abnormalRemark: undefined,
  abnormalCountermeasures: undefined,
  actualWork: undefined,
  remark: undefined
})

const formRules = reactive({
  productionLine: [{ required: true, message: '请选择生产产线', trigger: 'blur' }],
  teamLeader: [{ required: true, message: '请输入班组长', trigger: 'blur' }],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

// 新增监听器存储对象 - 使用 WeakMap 来避免索引问题
const rowWatchers = ref(new Map<any, () => void>())

// 新增表身行
const addBodyRow = () => {
  let lastEndTime = ''
  let lastStrTime = ''
  if (bodyData.value.length > 0) {
    lastEndTime = bodyData.value[bodyData.value.length - 1].productionTimeEnd || ''
    lastStrTime = bodyData.value[bodyData.value.length - 1].productionTimeStart || ''
  }
  const number = headerData.value.assembledNum || 0
  const standardWork = headerData.value.standardWork || 0
  const type = headerData.value.type || 0
  bodyData.value.push(createEmptyBodyRow(lastEndTime, lastStrTime, number, standardWork, type))

  // 重新收集输入框引用
  refreshNavigableInputs()
}

// 删除行时清理监听
const removeBodyRow = (index: number) => {
  //最后一行不允许删除
  if (bodyData.value.length === 1) {
    message.error('最后一行不允许删除！')
    return
  }

  // 获取要删除的行对象
  const rowToDelete = bodyData.value[index]

  // 清理该行的监听器
  if (rowWatchers.value.has(rowToDelete)) {
    rowWatchers.value.get(rowToDelete)!()
    rowWatchers.value.delete(rowToDelete)
  }

  // 删除行
  bodyData.value.splice(index, 1)

  // 重新收集输入框引用
  refreshNavigableInputs()
}

/** 打开弹窗 */
const open = async (type: string, batchesId?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (batchesId) {
    formLoading.value = true
    try {
      const data = await DayApi.getDay(batchesId)
      // 分离表头和表身数据
      headerData.value = {
        productionLine: data[0].productionLine,
        teamLeader: data[0].teamLeader,
        dateStr: data[0].dateStr,
        requiredAttendanceNum: data[0].requiredAttendanceNum,
        actualAttendanceNum: data[0].actualAttendanceNum,
        assembledNum: data[0].assembledNum,
        assembledTotal: data[0].assembledTotal,
        standardWork: data[0].standardWork,
        remarkHead: data[0].remarkHead,
        batchesId: data[0].batchesId,
        numberWork: data[0].numberWork,
        type: data[0].type
      }
      // 如果是修改
      bodyData.value = data.map((item) => ({
        id: item.id,
        productionTimeStart: item.productionTime.split('-')[0],
        productionTimeEnd: item.productionTime.split('-')[1],
        productionTime: item.productionTime,
        productionOrderCode: item.productionOrderCode,
        salesOrderCode: item.salesOrderCode,
        productNo: item.productNo,
        modelsOrColor: item.modelsOrColor,
        workOrderNum: item.workOrderNum,
        units: item.units,
        number: item.number,
        hoursReportNum: item.hoursReportNum,
        totalReportNum: item.totalReportNum,
        standardWork: item.standardWork,
        actualWork: item.actualWork,
        abnormalWork: item.abnormalWork,
        abnormalNum: item.abnormalNum,
        abnormalRemark: item.abnormalRemark,
        abnormalCountermeasures: item.abnormalCountermeasures,
        type: item.type,
        audit: item.audit,
        remark: item.remark
      }))

      // 初始化 originalReportNums 和 rowTotalReportNums
      bodyData.value.forEach(row => {
        const key = `${row.productionOrderCode}-${row.type}`
        originalReportNums.value[key] = row.hoursReportNum || 0
        rowTotalReportNums.value[key] = row.totalReportNum || 0
      })

      // 为编辑模式下的所有行设置监听器
      // bodyData.value.forEach(row => {
      //   setupRowWatcher(row)
      // })
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时，默认添加一行表身数据
    bodyData.value = [createEmptyBodyRow()]
    // setupRowWatcher(bodyData.value[0])

    //赋值当前登录用户为班组长
    //查询当前用户上一条表头信息
    const data = await DayApi.getPrevious()
    if (data) {
      headerData.value.productionLine = data.productionLine
      headerData.value.dateStr = dateUtil(new Date()).format('YYYY-MM-DD')
        ; (headerData.value.requiredAttendanceNum = data.requiredAttendanceNum),
          (headerData.value.actualAttendanceNum = data.actualAttendanceNum),
          (headerData.value.assembledNum = data.assembledNum),
          (headerData.value.remarkHead = data.remarkHead),
          (headerData.value.type = data.type)
      syncBodyNumber()
    } else {
      headerData.value.teamLeader = useUserStore().user.nickname
      headerData.value.dateStr = dateUtil(new Date()).format('YYYY-MM-DD')
      headerData.value.type = 1
      headerData.value.productionLine = 1
    }

    headerData.value.teamLeader = useUserStore().user.nickname
    totalReportNum.value = 0;
    //重置批次号
    headerData.value.batchesId = undefined;
  }

  // 等待DOM更新后重新收集输入框引用
  nextTick(() => {
    refreshNavigableInputs()
  })
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// 处理异常工时表单提交成功事件
const handleAbnormalSuccess = (data: any) => {
  console.log('接收到异常工时数据:', data)
  if(mobile.value){
    // 移动端
    const num = data.id
    bodyData.value[num].abnormalNum = data.abnormalNum
    bodyData.value[num].abnormalWork = data.abnormalWork
    bodyData.value[num].abnormalRemark = data.abnormalRemark
    bodyData.value[num].abnormalCountermeasures = data.abnormalCountermeasures
  }else{
    // PC端
    if(data.length===0){
      bodyData.value.forEach((item:any) => {
          item.abnormalNum =0
          item.abnormalWork = 0
          item.abnormalRemark = ''
          item.abnormalCountermeasures = ''
      })
    }else{
    bodyData.value.forEach((item:any) => {
      data.forEach((row:any) => { 
        if (item.id === row.id||(
          item.productionTimeStart === row.productionTimeStart &&
          item.productionTimeEnd === row.productionTimeEnd &&
          item.productionOrderCode === row.productionOrderCode &&
          item.salesOrderCode === row.salesOrderCode &&
          item.productNo === row.productNo &&
          item.type === row.type
        )) { 
          item.abnormalNum = row.abnormalNum
          item.abnormalWork = row.abnormalWork
          item.abnormalRemark = row.abnormalRemark
          item.abnormalCountermeasures = row.abnormalCountermeasures
        }
      })
    })
    }
  }

}
//处理出勤工时表单提交成功事件
const handleAttendanceSuccess = (data: any) => {
  console.log('接收到出勤工时数据:', data)

  headerData.value.batchesId = data.batchesId
  headerData.value.numberWork = data.attendanceWorkingHours
  headerData.value.assembledTotal=data.assembledTotal
}
// Add validation rules for body form
const validateBodyForm = () => {
  // 检查：同一工单号 + 同一工序不能重复添加
  // const uniqueKeys = new Set<string>();
  for (let i = 0; i < bodyData.value.length; i++) {
    const row = bodyData.value[i];
    // const key = `${row.productionOrderCode}-${row.type}`; // 使用组合键判断唯一性

    // if (uniqueKeys.has(key)) {
    //   message.error(`第${i + 1}行：工单号【${row.productionOrderCode}】与工序【${getLabelByType(row.type)}】组合已存在，请勿重复添加`);
    //   return false;
    // }

    // if (row.productionOrderCode && row.type !== undefined) {
    //   uniqueKeys.add(key);
    // }
    if (!row.productionTimeStart || !row.productionTimeEnd) {
      message.error(`第${i + 1}行：请选择时间段`)
      return false
    }
    if (!row.workOrderNum) {
      message.error(`第${i + 1}行：工单数量不能为空，请选择生产工单号或销售订单号`)
      return false
    }
    if (!row.number) {
      message.error(`第${i + 1}行：人数不能为空，请输入出勤人数`)
      return false
    }
    if (!row.hoursReportNum) {
      message.error(`第${i + 1}行：请输入生产数`)
      return false
    }
  }
  return true
}



/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  if (bodyData.value.length === 0) {
    message.error('请至少添加一行表身数据')
    return
  }

  // 校验表身数据
  if (!validateBodyForm()) {
    return
  }

  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      // 新增时，将表头数据合并到每一行表身数据中，组成数组一次性提交
      const createData = bodyData.value.map((bodyRow) => ({
        ...headerData.value,
        ...bodyRow,
        productionTime: `${bodyRow.productionTimeStart || ''}-${bodyRow.productionTimeEnd || ''}`,
        type: bodyRow.type
      })) as DayVO[]
      await DayApi.createDay(createData)
      message.success(t('common.createSuccess'))
    } else {
      // 修改时
      await DayApi.batchUpdateDays(
        bodyData.value.map((row) => ({
          ...headerData.value,
          ...row,
          type: row.type,
          productionTime: `${row.productionTimeStart || ''}-${row.productionTimeEnd || ''}`
        }))
      )
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/* 单头组装人数、标准工时填写同步至单身 */
const syncBodyNumber = () => {
  bodyData.value.forEach((row) => {
    row.number = headerData.value.assembledNum
    row.standardWork = headerData.value.standardWork
  })
}
/** 重置表单 */
const resetForm = () => {
  // 清理所有监听器
  rowWatchers.value.forEach((stop) => {
    stop()
  })
  rowWatchers.value.clear()

  // 清理输入框引用
  navigableInputRefs.value.clear()

  headerData.value = {
    teamLeader: '',
    dateStr: '',
    requiredAttendanceNum: undefined,
    actualAttendanceNum: undefined,
    assembledNum: undefined,
    assembledTotal: undefined,
    remarkHead: undefined,
    standardWork: undefined,
    batchesId: undefined,
    numberWork: undefined,
    type: 1,
    productionLine: 1
  }
  bodyData.value = []
  originalReportNums.value = {}
  rowTotalReportNums.value = {}
  formRef.value?.resetFields()
}

// 存储每个工单的原始生产数值
const originalReportNums = ref<Record<string, number>>({})
// 记录每行的累计数
const rowTotalReportNums = ref<Record<string, number>>({})
const validateHoursReportNum = (val: string, row: any) => {
  const numVal = Number(val)

  if (numVal > row.workOrderNum) {
    row.hoursReportNum = row.workOrderNum
    message.warning('小时完成数量不能超过工单数量')
  } else {
    const orderId = `${row.productionOrderCode}-${row.type}`

    const currentOriginal = originalReportNums.value[orderId] || 0
    const change = numVal - currentOriginal

    // 更新该行的累计值
    rowTotalReportNums.value[orderId] = (rowTotalReportNums.value[orderId] || 0) + change

    // 更新原始值记录
    originalReportNums.value[orderId] = numVal

    // 将当前累计值写入 row.totalReportNum
    row.totalReportNum = rowTotalReportNums.value[orderId]
  }
}

</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

// 自动完成组件样式
.autocomplete-item {
  .main-text {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .sub-text {
    font-size: 12px;
    color: #909399;
    line-height: 1.2;
  }
}

.no-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 8px 0;
}

.time-range-row {
  display: flex;
  align-items: center;

  .time-select {
    margin: 0 4px;
  }

  .time-range-separator {
    margin: 0 6px;
    font-size: 16px;
    color: #606266;
    line-height: 32px;
  }
}

.card-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  text-align: center;
}

@media (max-width: 768px) {
  .mobile-body-form :deep(.el-form-item__label) {
    text-align: right !important;
    width: 80px !important;
    display: inline-block;
  }

  :deep(.el-form-item__content .el-input-group) {
    margin-top: 5px !important;
  }
}

:deep(.el-form-item__label) {
  font-size: 12px !important;
}

:deep(.el-card__body),
:deep(.el-card__header) {
  padding: 2px !important;
}

:deep(.el-scrollbar__thumb) {
  background-color: #000000 !important;
}

:deep(.el-form-item) {
  margin-bottom: 2px !important;
}

:deep(.el-table__cell) {
  padding: 1px 0 !important;

  .cell {
    padding: 1px 0 !important;
  }

  .el-select__wrapper {
    box-shadow: none !important;
  }
}

.custom-card :deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.custom-card) {
  transform: translateY(-17px);
}

:deep(.el-input__prefix) {
  display: none !important;
}

:deep(.el-select__prefix) {
  display: none !important;
}

/* 新增针对数字输入框的样式 */
:deep(.no-spin-input) {

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
    /* Firefox */
  }
}

.menuDiv {
  display: none;
  position: absolute;

  .menuUl {
    height: auto;
    width: auto;
    font-size: 14px;
    text-align: left;
    border-radius: 4px;
    border: 1px solid #DADADA;
    /* 浅灰色细边框 */
    background-color: #F5F5F5;
    color: #333333;
    list-style: none;
    padding: 0 10px;

    li {
      width: 140px;
      height: 35px;
      line-height: 35px;
      cursor: pointer;
      border-bottom: 1px solid #EAEAEA;
      /* 分隔线 */

      &:hover {
        background-color: #E6F7FF;
        /* 浅蓝渐变高亮 */
        color: #2C3E50;
        /* 深蓝色字体 */
      }
    }
  }
}
</style>

<style lang="scss">
// 移动端弹窗样式
@media screen and (max-width: 768px) {
  .el-dialog {
    margin: 0 !important;
    height: 100vh;
    max-width: 100vw;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
  }

  .el-dialog__header {
    padding: 10px;
    margin-right: 0;
  }

  .el-dialog__footer {
    padding: 10px;
    border-top: 1px solid #dcdfe6;
  }

  .el-input__wrapper {
    box-shadow: none !important;
    padding: 0px 0px !important;
  }

  .el-select__wrapper {
    box-shadow: none !important;
  }

  .el-form-item {
    box-shadow: 0 2px 6px rgba(64, 64, 65, 0.1); // 更淡的蓝紫色阴影
    transition: box-shadow 0.2s ease; // 缩短过渡时间
    margin-bottom: 0px !important;
    height: 40px !important;
  }

  .el-form-item__label,
  .el-form-item__content {
    height: 40px !important;
    align-items: center !important;
    line-height: 40px !important;
  }
}

// 全局样式，用于自动完成下拉框
.production-order-autocomplete,
.sales-order-autocomplete {
  .el-autocomplete-suggestion__list {
    max-height: 200px;
    overflow-y: auto;
  }

  .el-autocomplete-suggestion__item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
      background-color: #f5f7fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
