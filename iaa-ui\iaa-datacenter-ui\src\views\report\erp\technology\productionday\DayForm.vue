<template>
  <div class="dialogContainer" v-show="dialogVisible" v-resizer>
    <Dialog :title="dialogTitle" v-model="dialogVisible" :width="'100%'" fullscreen v-dialogDrag>
      <el-form ref="formRef" :model="headerData" :rules="formRules" label-width="80px" v-loading="formLoading">
        <!-- 表头信息 -->
        <el-card class="mb-4" shadow="never">
          <el-row>
            <el-col :span="4" :xs="12">
              <el-form-item label="产线" :label-width="mobile ? '' : '70px'" prop="productionLine">
                <el-select v-model="headerData.productionLine" placeholder="请选择产线">
                  <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_LINE)" :key="dict.value"
                    :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="班组长" :label-width="mobile ? '' : '58px'" prop="teamLeader">
                <el-input v-model="headerData.teamLeader" placeholder="请输入班组长" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="日期" :label-width="mobile ? '' : '36px'" prop="dateStr">
                <el-date-picker
v-model="headerData.dateStr" type="date" value-format="x" placeholder="选择日期"
                  style="width: 100%" :prefix-icon="customPrefix" class="custom-date-picker"  />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="应出勤" :label-width="mobile ? '' : '72px'" prop="requiredAttendanceNum">
                <el-input v-model="headerData.requiredAttendanceNum" style="width: 100%" type="number" min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="实际出勤" :label-width="mobile ? '' : '84px'" prop="actualAttendanceNum">
                <el-input v-model="headerData.actualAttendanceNum" style="width: 100%" type="number" min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="组装人数" :label-width="mobile ? '' : '72px'" prop="assembledNum">
                <el-input
v-model="headerData.assembledNum" style="width: 100%" type="number" min="0"
                  @change="syncBodyNumber" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- <el-col :span="4" :xs="12">
              <el-form-item label="组装总工时" :label-width="mobile ? '' : '72px'" prop="assembledTotal">
                <el-input
v-model="headerData.assembledTotal" style="width: 100%"
                  oninput="value=value.replace(/[^0-9.]/g,'')" :readonly="true" placeholder="系统自动计算" />
              </el-form-item>
            </el-col> -->
            <el-col :span="4" :xs="12">
              <el-form-item label="工序" :label-width="mobile ? '' : '70px'" prop="type">
                <el-select v-model="headerData.type" placeholder="工序" style="width: 100%">
                  <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)" :key="dict.value"
                    :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="标准工时" :label-width="mobile ? '' : '60px'" prop="standard_work">
                <el-input
v-model="headerData.standardWork" type="number" :min="0" style="width: 100%"
                  @change="syncBodyNumber">
                  <template #append>分</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="4" :xs="24">
              <el-form-item label="备注" :label-width="mobile ? '' : '36px'" prop="remarkHead">
                <el-input v-model="headerData.remarkHead" placeholder="请输入备注" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="出勤工时" :label-width="mobile ? '' : '72px'">
                <el-input
v-model="headerData.numberWork" type="number" placeholder="系统自动计算" :min="0"
                  style="width: 100%" :readonly="true" />
              </el-form-item>
            </el-col>
            <el-col :span="2" :xs="12">
              <el-button  @click="openFormAttendance('create', headerData.batchesId, '出勤工时录入')" class="w-full" plain>
                出勤工时录入
              </el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 表身信息 -->
        <el-card shadow="never" class="custom-card">
          <template #header v-if="!mobile">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <el-button type="primary" @click="addBodyRow"  size="small" plain>
                <Icon icon="ep:plus" style="margin-right: 4px" /> 新增行
              </el-button>
            </div>
          </template>
          <div v-if="mobile">
            <div v-for="(row, index) in bodyData" :key="index" class="card-item">
              <div class="card-title">第 {{ index + 1 }} 条 / 共 {{ bodyData.length }} 条 <span>                   
                 <el-button  @click.stop="addBodyRow" :icon="Plus" size="small">
                      新增行
                    </el-button>
                  </span>
                  </div>
              <el-form class="mobile-body-form">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="时间段">
                      <div class="time-range-row">
                        <el-time-select
v-model="row.productionTimeStart" placeholder="起始时间" :start="'08:30'"
                          :end="'23:30'" :step="'00:15'" placement="top-start" />
                        <span class="time-range-separator">-</span>
                        <el-time-select
v-model="row.productionTimeEnd" placeholder="结束时间" :start="'08:30'"
                          :end="'23:30'" :step="'00:15'" :min-time="row.productionTimeStart" placement="top-start" />
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="工单号">
                      <el-autocomplete
v-model="row.productionOrderCode" :fetch-suggestions="queryProductionOrder"
                        placeholder="输入工单号" style="width: 100%"
                        @select="(item) => handleProductionOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" clearable popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.productionOrderCode }}-{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="销售订单号">
                      <el-autocomplete
v-model="row.salesOrderCode" :fetch-suggestions="querySalesOrder"
                        placeholder="输入销售订单号" style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" clearable popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="品号">
                      <el-autocomplete
v-model="row.productNo" :fetch-suggestions="(queryString, cb) =>
                          queryProductNo(
                            queryString,
                            cb,
                            row.productionOrderCode,
                            row.salesOrderCode
                          )
                        " placeholder="输入品号查询" style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" clearable popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      <el-input v-model="row.productNo" placeholder="品号" />
                    </el-form-item>
                  </el-col>
                 <el-col :span="12">
                    <el-form-item label="工序">
                    <template #default>
                <el-select v-model="row.type" placeholder="请选择工序" style="width: 100%" @change="(value) => handleTypeChange(value, row)">
                  <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)" :key="dict.value"
                    :label="dict.label" :value="dict.value"  />
                </el-select>
              </template>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品名">
                      <el-input v-model="row.modelsOrColor" placeholder="机型/颜色" readonly />
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="数量">
                      <el-input v-model="row.workOrderNum" placeholder="数量" readonly />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="人数">
                      <el-input v-model="row.number" placeholder="人数" min="0" type="number" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="生产数">
                      <el-input
v-model="row.hoursReportNum" placeholder="生产数量" type="number" :min="0"
                        :max="row.workOrderNum" @input="(val) => validateHoursReportNum(val, row)" />
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="累计数">
                      <el-input 
                      v-model="row.totalReportNum" placeholder="累计数" :readonly="true" type="number"
                        :min="0" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="备注">
                      <el-input v-model="row.remark" placeholder="备注" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="标准工时">
                      <el-input v-model="row.standardWork">
                        <template #append>分</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="row.abnormalWork">
                    <el-form-item label="异常总工时">
                      <el-input v-model="row.abnormalWork" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <!-- <el-col :span="8">
                    <el-button type="primary" plain @click="addBodyRow" class="w-full">
                      新增行
                    </el-button>
                  </el-col> -->
                  <el-col :span="12">
                    <el-button  plain @click="openForm(index, '异常工时录入')" class="w-full">
                      异常工时
                    </el-button>
                  </el-col>
                  <el-col :span="12">
                    <el-button  plain @click="removeBodyRow(index)" class="w-full">
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <!-- <div style="display: flex; justify-content: space-between; align-items: center">
              <el-button type="primary" @click="addBodyRow" v-if="formType === 'create'" size="small" plain>
                <Icon icon="ep:plus" style="margin-right: 4px" /> 新增行
              </el-button>
            </div> -->
          </div>
          <el-table
v-else :data="bodyData" border stripe scrollbar-always-on max-height="540"
            style="min-height: 250px">
            ">
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="时间段" width="245">
              <template #default="{ row }">
                <div class="time-range-row">
                  <el-time-select
v-model="row.productionTimeStart" placeholder="起始时间" :start="'08:30'" :end="'23:30'"
                    :step="'00:15'" placement="top-start" />
                  <span class="time-range-separator">-</span>
                  <el-time-select
v-model="row.productionTimeEnd" placeholder="结束时间" :start="'08:30'" :end="'23:30'"
                    :step="'00:15'" :min-time="row.productionTimeStart" placement="top-start" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="工序" width="120">
              <template #default="{ row }">
                <el-select v-model="row.type" placeholder="请选择工序" style="width: 100%">
                  <el-option
v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)" :key="dict.value"
                    :label="dict.label" :value="dict.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="工单号">
              <template #default="{ row, $index }">
                <el-autocomplete
v-model="row.productionOrderCode" :fetch-suggestions="queryProductionOrder"
                  placeholder="输入工单号" style="width: 100%" @select="(item) => handleProductionOrderSelect(item, $index)"
                  :trigger-on-focus="false" :debounce="300" clearable popper-class="production-order-autocomplete"
                  placement="top-start">
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text">{{ item.productionOrderCode }}-{{ item.productNo }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column label="销售订单号">
              <template #default="{ row, $index }">
                <el-autocomplete
v-model="row.salesOrderCode" :fetch-suggestions="querySalesOrder" placeholder="输入销售订单号"
                  style="width: 100%" @select="(item) => handleSalesOrderSelect(item, $index)" :trigger-on-focus="false"
                  :debounce="300" clearable popper-class="production-order-autocomplete" placement="top-start">
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column label="品号" width="120">
              <!-- <template #default="{ row }">
                <el-input v-model="row.productNo" placeholder="品号" readonly />
              </template> -->
              <template #default="{ row, $index }">
                <el-autocomplete
v-model="row.productNo" :fetch-suggestions="(queryString, cb) =>
                    queryProductNo(queryString, cb, row.productionOrderCode, row.salesOrderCode)
                  " placeholder="输入品号查询" style="width: 100%" @select="(item) => handleSalesOrderSelect(item, $index)"
                  :trigger-on-focus="false" :debounce="300" clearable popper-class="production-order-autocomplete"
                  placement="top-start">
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text">{{ item.productNo }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column label="机型/颜色（品名）" width="150">
              <template #default="{ row }">
                <el-input v-model="row.modelsOrColor" placeholder="机型/颜色" readonly />
              </template>
            </el-table-column>
            <!-- <el-form-item label="标准工时" :label-width="mobile ? '' : '60px'" prop="standard_work">
                <el-input v-model="headerData.standardWork"  type="number" :min="0" style="width: 100%" />
              </el-form-item> -->
            <el-table-column label="工单数量" width="100">
              <template #default="{ row }">
                <el-input v-model="row.workOrderNum" placeholder="工单数量" readonly />
              </template>
            </el-table-column>
            <el-table-column label="单位" width="80">
              <template #default="{ row }">
                <el-input v-model="row.units" placeholder="单位" readonly />
              </template>
            </el-table-column>
            <el-table-column label="标准工时" width="80">
              <template #default="{ row }">
                <el-input v-model="row.standardWork" placeholder="标准工时" />
              </template>
            </el-table-column>
            <el-table-column label="人数" width="80">
              <template #default="{ row }">
                <el-input v-model="row.number" placeholder="人数" type="number" />
              </template>
            </el-table-column>
            <el-table-column label="生产数" width="80">
              <template #default="{ row }">
                <el-input
v-model="row.hoursReportNum" placeholder="生产数" type="number" :min="0" :max="row.workOrderNum"
                  @input="(val) => validateHoursReportNum(val, row)" />
              </template>
            </el-table-column>
            <el-table-column label="备注" width="120">
              <template #default="{ row }">
                <el-input v-model="row.remark" placeholder="备注" />
              </template>
            </el-table-column>
                        <el-table-column label="异常人数" width="80">
              <template #default="{ row }">
                <el-input
                  v-model="row.abnormalNum" placeholder="异常人数" type="number" :min="0" />
              </template>
            </el-table-column>
            <el-table-column label="异常工时" width="80">
              <template #default="{ row }">
                <el-input
                  v-model="row.abnormalWork" placeholder="异常工时" type="number" :min="0"  />
              </template>
            </el-table-column>
            <el-table-column label="异常问题点">
              <template #default="{ row }">
                <el-input v-model="row.abnormalRemark" placeholder="问题点" />
              </template>
            </el-table-column>
                        <el-table-column label="异常对策">
              <template #default="{ row }">
                <el-input v-model="row.abnormalCountermeasures" placeholder="异常对策" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="{ $index }">
                <!-- <el-button type="warning" link @click="openForm($index,'create', headerData.batchesId, '异常工时录入')"> 异常工时 </el-button> -->
                <el-button type="danger" link @click="removeBodyRow($index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-form>
      <template #footer>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </Dialog>
  </div>
  <AbnormalForm ref="formRefAbnormal" @success="handleAbnormalSuccess" />
  <AttendanceForm ref="formRefAttendance" @success="handleAttendanceSuccess" />
</template>
<script setup lang="ts">
import { DayApi, DayVO } from '@/api/report/technology/production'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { dateUtil } from '@/utils/dateUtil'
import { ref, DirectiveBinding} from 'vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import AbnormalForm from './AbnormalForm.vue'
import AttendanceForm from './AttendanceForm.vue'
import _ from 'lodash'
import { Plus} from '@element-plus/icons-vue'

/** 生产日报 表单 */
defineOptions({ name: 'DayForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)
// 查询生产工单号
const queryProductionOrder = (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }

  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ productionOrderCode: '加载中...', loading: true }])

  DayApi.getWorkSalesOptions(queryString, 0) // type=0 表示生产工单号查询
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ productionOrderCode: '暂无数据', noData: true }])
    })
    .catch(() => {
      cb([{ productionOrderCode: '查询失败', error: true }])
    })
}

//异常工时录入、出勤录入
const formRefAbnormal = ref()
const formRefAttendance = ref()
const openForm = (index:number, title?: any) => {
  //判断时间段是否为空，若为空则提示用户先选择时间段
  // if(bodyData.value[index].productionTimeStart === ''|| bodyData.value[index].productionTimeEnd === ''){
  //   message.error('请先选择时间段！')
  // }
    formRefAbnormal.value.open(bodyData.value[index],index, title)

  
}

const openFormAttendance = (type: string, batchesId?: any, title?: any) => {
  formRefAttendance.value.open(type, batchesId, title)
}


// 查询销售订单号
const querySalesOrder = (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }

  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ salesOrderCode: '加载中...', loading: true }])

  DayApi.getWorkSalesOptions(queryString, 1)
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ salesOrderCode: '暂无数据', noData: true }])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([{ salesOrderCode: '查询失败', error: true }])
    })
}
// 查询品号
const queryProductNo = (
  queryString: string,
  cb: Function,
  productionOrderCode: string,
  salesOrderCode: string
) => {
  if (!queryString) {
    cb([])
    return
  }
  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ productNo: '加载中...', loading: true }])
  //把工单号和销售订单号和输入的品号都传给后端
  const data = {
    productionOrderCode: productionOrderCode, // 生产工单号
    salesOrderCode: salesOrderCode, // 销售订单号
    productNo: queryString // 输入的品号
  }
  DayApi.getProductNo(data)
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ productNo: '暂无数据', noData: true }])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([{ productNo: '查询失败', error: true }])
    })
}
// 选择生产工单号后的处理
const handleProductionOrderSelect = async (item: any, rowIndex: number) => {
  if (item.loading || item.noData || item.error) return

  const row = bodyData.value[rowIndex]
  if (row && item) {
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.workOrderNum = item.workOrderNum
    row.units = item.units

    const dayVO: any = {
      productionOrderCode: item.productionOrderCode,
      salesOrderCode: item.salesOrderCode,
      type: row.type,
      dateStr: headerData.value.dateStr
    }

    const data = await DayApi.getOrderCodeNum(dayVO)
    const key = `${row.productionOrderCode}-${row.type}`

    // 设置初始累计值
    rowTotalReportNums.value[key] = data || 0
    row.totalReportNum = data || 0

    // 如果该订单已有生产数，则用已有的生产数作为原始值
    originalReportNums.value[key] = row.hoursReportNum || 0
  }
}
const customPrefix = shallowRef({
  render() {
    return null
  }
})
// 选择销售订单号后的处理
const handleSalesOrderSelect = async (item: any, rowIndex: number) => {
  // 忽略加载状态、无数据状态、错误状态的选择
  if (item.loading || item.noData || item.error) {
    return
  }

  const row = bodyData.value[rowIndex]
  if (row && item) {
    // 设置生产工单号
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.workOrderNum = item.workOrderNum
    row.units = item.units
    handleTypeChange(row.type, row)
  }
}

// 切换工序时查询累计数
const handleTypeChange = async (value: number, row: any) => {
  if (!row.productionOrderCode) return

  const dayVO: any = {
    productionOrderCode: row.productionOrderCode,
    salesOrderCode: row.salesOrderCode,
    type: value,
    dateStr: headerData.value.dateStr
  }

  const data = await DayApi.getOrderCodeNum(dayVO)
  const key = `${row.productionOrderCode}-${value}`

  row.type = value
  rowTotalReportNums.value[key] = data || 0
  row.totalReportNum = data || 0

  // 如果该订单已有生产数，则用已有的生产数作为原始值
  originalReportNums.value[key] = row.hoursReportNum || 0

    // 新增：主动触发校验
  checkAbnormalWorkExists(row, bodyData.value.indexOf(row))
}

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const dialogSizeMap = new Map<string, { width: string; height: string }>()

const totalReportNum = ref(0)

const vResizer = {
  updated: (el: HTMLElement, binding: DirectiveBinding) => {
    // 弹框可拉伸最小宽高
    const minWidth = 1000
    const minHeight = 300

    // 获取对话框ID（用于存储尺寸）
    const dialogId = binding.value?.id || 'default-dialog'

    // 获取弹框头部（这部分可双击全屏）
    const dialogHeaderEl = el.querySelector('.el-dialog__header') as HTMLElement | null
    // 弹窗
    const dragDom = el.querySelector('.el-dialog') as HTMLElement | null as any

    // 如果关键元素不存在，则直接返回，避免报错
    if (!dialogHeaderEl || !dragDom) return

    // 给弹窗加上overflow auto；不然缩小时框内的标签可能超出dialog；
    dragDom.style.overflow = 'auto'

    // 清除选择头部文字效果
    dialogHeaderEl.onselectstart = new Function('return false') as any

    // 头部加上可拖动cursor
    dialogHeaderEl.style.cursor = 'move'

    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null)

    // 检查是否有保存的尺寸并应用
    const savedSize = dialogSizeMap.get(dialogId)
    if (savedSize) {
      dragDom.style.width = savedSize.width
      dragDom.style.height = savedSize.height
    }

    // 移动功能
    let moveDown = (e: MouseEvent) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft
      const disY = e.clientY - dialogHeaderEl.offsetTop

      // 获取到的值带px 正则匹配替换
      let styL = 0
      let styT = 0

      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      styL = +sty.left.replace(/px/g, '')
      styT = +sty.top.replace(/px/g, '')

      document.onmousemove = function (e) {
        // 通过事件委托，计算移动的距离
        const l = e.clientX - disX
        const t = e.clientY - disY

        // 移动当前元素
        dragDom.style.left = `${l + styL}px`
        dragDom.style.top = `${t + styT}px`
      }

      document.onmouseup = function () {
        document.onmousemove = null
        document.onmouseup = null
      }
    }
    dialogHeaderEl.onmousedown = moveDown

    // 拉伸(右下方)
    let resizeEl = document.createElement('div')
    dragDom.appendChild(resizeEl)
    // 在弹窗右下角加上一个10-10px的控制块
    resizeEl.style.cursor = 'se-resize'
    resizeEl.style.position = 'absolute'
    resizeEl.style.height = '10px'
    resizeEl.style.width = '10px'
    resizeEl.style.right = '0px'
    resizeEl.style.bottom = '0px'
    resizeEl.style.zIndex = '99'
    // 添加缩放图标
    resizeEl.innerHTML =
      '<i class="el-icon-zoom-in" style="font-size: 10px; position: absolute; right: 0; bottom: 0;"></i>'

    // 鼠标拉伸弹窗
    resizeEl.onmousedown = (e) => {

      // 鼠标按下，计算当前元素距离可视区的距离
      let disX = e.clientX - resizeEl.offsetLeft
      let disY = e.clientY - resizeEl.offsetTop

      document.onmousemove = function (e) {
        e.preventDefault() // 移动时禁用默认事件

        // 通过事件委托，计算移动的距离
        let x = e.clientX - disX
        let y = e.clientY - disY

        // 比较是否小于最小宽高
        dragDom.style.width = x > minWidth ? `${x}px` : minWidth + 'px'
        dragDom.style.height = y > minHeight ? `${y}px` : minHeight + 'px'

        // 保存当前尺寸
        saveDialogSize(dialogId, dragDom)

        changeContentSize(dragDom)
      }

      // 拉伸结束
      document.onmouseup = function () {
        document.onmousemove = null
        document.onmouseup = null
      }
    }

    // 拉伸(左下方) - 添加缩放图标
    let resizeLB = document.createElement('div')
    dragDom.appendChild(resizeLB)
    resizeLB.style.cursor = 'sw-resize'
    resizeLB.style.position = 'absolute'
    resizeLB.style.height = '10px'
    resizeLB.style.width = '10px'
    resizeLB.style.left = '0px'
    resizeLB.style.bottom = '0px'
    resizeLB.style.zIndex = '99'
    resizeLB.innerHTML =
      '<i class="el-icon-zoom-out" style="font-size: 10px; position: absolute; left: 0; bottom: 0;"></i>'

    // 鼠标拉伸弹窗
    resizeLB.onmousedown = () => {
      let initialWidth = dragDom.clientWidth
      let initialLeft = dragDom.offsetLeft

      document.onmousemove = function (e) {
        e.preventDefault()

        // 计算新宽度（从左侧拉伸）
        const newWidth = initialWidth + (initialLeft - e.clientX)

        // 确保不小于最小宽度
        if (newWidth >= minWidth) {
          dragDom.style.width = `${newWidth}px`
          dragDom.style.left = `${e.clientX}px`
        }

        // 计算新高度（从底部拉伸）
        const newHeight = e.clientY - dragDom.offsetTop
        if (newHeight >= minHeight) {
          dragDom.style.height = `${newHeight}px`
        }

        // 保存当前尺寸
        saveDialogSize(dialogId, dragDom)

        changeContentSize(dragDom)
      }

      document.onmouseup = function () {
        document.onmousemove = null
        document.onmouseup = null
      }
    }

    // 拉伸(右边)
    let resizeElR = document.createElement('div')
    dragDom.appendChild(resizeElR)
    resizeElR.style.cursor = 'ew-resize'
    resizeElR.style.position = 'absolute'
    resizeElR.style.height = '100%'
    resizeElR.style.width = '5px'
    resizeElR.style.right = '0px'
    resizeElR.style.top = '0px'

    // 鼠标拉伸弹窗
    resizeElR.onmousedown = (e) => {
      let initialWidth = dragDom.clientWidth
      let initialX = e.clientX

      document.onmousemove = function (e) {
        e.preventDefault()

        // 计算新宽度
        const newWidth = initialWidth + (e.clientX - initialX)

        // 确保不小于最小宽度
        if (newWidth >= minWidth) {
          dragDom.style.width = `${newWidth}px`

          // 保存当前尺寸
          saveDialogSize(dialogId, dragDom)

          changeContentSize(dragDom)
        }
      }

      document.onmouseup = function () {
        document.onmousemove = null
        document.onmouseup = null
      }
    }

    // 拉伸(左边)
    let resizeElL = document.createElement('div')
    dragDom.appendChild(resizeElL)
    resizeElL.style.cursor = 'ew-resize'
    resizeElL.style.position = 'absolute'
    resizeElL.style.height = '100%'
    resizeElL.style.width = '5px'
    resizeElL.style.left = '0px'
    resizeElL.style.top = '0px'

    // 鼠标拉伸弹窗
    resizeElL.onmousedown = () => {
      let initialWidth = dragDom.clientWidth
      let initialLeft = dragDom.offsetLeft

      document.onmousemove = function (e) {
        e.preventDefault()

        // 计算新宽度（从左侧拉伸）
        const newWidth = initialWidth + (initialLeft - e.clientX)

        // 确保不小于最小宽度
        if (newWidth >= minWidth) {
          dragDom.style.width = `${newWidth}px`
          dragDom.style.left = `${e.clientX}px`

          // 保存当前尺寸
          saveDialogSize(dialogId, dragDom)

          changeContentSize(dragDom)
        }
      }

      document.onmouseup = function () {
        document.onmousemove = null
        document.onmouseup = null
      }
    }

    // 拉伸(下边)
    let resizeElB = document.createElement('div')
    dragDom.appendChild(resizeElB)
    resizeElB.style.cursor = 'ns-resize'
    resizeElB.style.position = 'absolute'
    resizeElB.style.height = '5px'
    resizeElB.style.width = '100%'
    resizeElB.style.left = '0px'
    resizeElB.style.bottom = '0px'

    // 鼠标拉伸弹窗
    resizeElB.onmousedown = (e) => {
      let initialHeight = dragDom.clientHeight
      let initialY = e.clientY

      document.onmousemove = function (e) {
        e.preventDefault()

        // 计算新高度
        const newHeight = initialHeight + (e.clientY - initialY)

        // 确保不小于最小高度
        if (newHeight >= minHeight) {
          dragDom.style.height = `${newHeight}px`

          // 保存当前尺寸
          saveDialogSize(dialogId, dragDom)

          changeContentSize(dragDom)
        }
      }

      document.onmouseup = function () {
        document.onmousemove = null
        document.onmouseup = null
      }
    }
  }
}

// 保存对话框尺寸
const saveDialogSize = (dialogId: string, dialogEl: HTMLElement) => {
  dialogSizeMap.set(dialogId, {
    width: dialogEl.style.width,
    height: dialogEl.style.height
  })
}

// 内容区域大小调整函数
const changeContentSize = (dialogEl: HTMLElement): void => {
  // 获取对话框内容区域元素
  const contentEl = dialogEl.querySelector('.el-dialog__body') as HTMLElement | null
  if (!contentEl) return

  // 获取对话框头部和底部元素
  const headerEl = dialogEl.querySelector('.el-dialog__header') as HTMLElement | null
  const footerEl = dialogEl.querySelector('.el-dialog__footer') as HTMLElement | null

  // 计算头部和底部高度
  const headerHeight = headerEl?.offsetHeight || 0
  const footerHeight = footerEl?.offsetHeight || 0

  // 获取对话框的计算样式
  const computedStyle = window.getComputedStyle(dialogEl)

  // 计算内容区域可用高度 (对话框高度 - 头部高度 - 底部高度 - 内边距 - 边框)
  const paddingTop = parseInt(computedStyle.paddingTop)
  const paddingBottom = parseInt(computedStyle.paddingBottom)
  const borderTop = parseInt(computedStyle.borderTopWidth)
  const borderBottom = parseInt(computedStyle.borderBottomWidth)

  const contentHeight =
    dialogEl.clientHeight -
    headerHeight -
    footerHeight -
    paddingTop -
    paddingBottom -
    borderTop -
    borderBottom

  // 设置内容区域高度
  contentEl.style.height = `${contentHeight}px`

  // 确保内容区域在需要时显示滚动条
  contentEl.style.overflow = 'auto'
}
const headerData = ref({
  productionLine: 1,
  teamLeader: '',
  dateStr: '',
  requiredAttendanceNum: undefined,
  actualAttendanceNum: undefined,
  assembledNum: undefined,
  assembledTotal: undefined,
  standardWork: undefined,
  remarkHead: undefined,
  batchesId: undefined,
  numberWork: undefined,
  type: 1
})

// 表身数据（其他字段）
const bodyData = ref<any[]>([])
// 创建空的表身行
const createEmptyBodyRow = (lastEndTime = '', lastStrTime = '', number = 0, standardWork = 0,type=1) => ({
  productionTimeStart: lastStrTime,
  productionTimeEnd: lastEndTime,
  type: type,
  productionOrderCode: undefined,
  salesOrderCode: undefined,
  productNo: undefined,
  modelsOrColor: undefined,
  workOrderNum: undefined,
  units: undefined,
  number: number,
  hoursReportNum: undefined,
  totalReportNum: undefined,
  standardWork: standardWork,
  abnormalWork: number,
  abnormalNum: number,
  abnormalRemark:  undefined,
  abnormalCountermeasures:  undefined,
  actualWork: undefined,
  remark: undefined
})

const formRules = reactive({
  productionLine: [{ required: true, message: '请选择生产产线', trigger: 'blur' }],
  teamLeader: [{ required: true, message: '请输入班组长', trigger: 'blur' }],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

// 新增监听器存储对象
const rowWatchers = ref(new Map<number, () => void>())
// 优化后的校验方法
const checkAbnormalWorkExists = async (row: any, rowIndex: number) => {
  // 添加调试日志
  console.log('校验行索引:', rowIndex, '当前行数据:', JSON.parse(JSON.stringify(row)))
  
  if (
    headerData.value.productionLine &&
    headerData.value.dateStr &&
    row.productionTimeStart &&
    row.productionTimeEnd &&
    row.type
  ) {
    try {
      const currentRow = bodyData.value[rowIndex]
      const params = {
        productionLine: headerData.value.productionLine,
        dateStr: headerData.value.dateStr,
        productionTime:  currentRow.productionTimeStart + '-' + currentRow.productionTimeEnd,
        type: currentRow.type
      }
      const res = await DayApi.checkAbnormalWorkExists(params)
      
      // 使用 Object.assign 避免触发重复更新
      if (res) {
        Object.assign(row, {
          abnormalNum: res.abnormalNum,
          abnormalWork: res.abnormalWork,
          abnormalRemark: res.abnormalRemark,
          abnormalCountermeasures: res.abnormalCountermeasures
        })
      }
    } catch (e) {
      console.error('校验异常工时失败:', e)
    }
  }
}

// 统一监听处理函数
const setupRowWatcher = (row: any, index: number) => {
  if (rowWatchers.value.has(index)) {
    rowWatchers.value.get(index)!()
    rowWatchers.value.delete(index)
  }
  
  
    // 使用防抖函数包装校验方法
  const debouncedCheck = _.debounce(() => {
    checkAbnormalWorkExists(row, index)
  }, 300)

  // 使用 watch 替代 watchEffect 精确监听字段
  const stop = watch(
    [
      () => headerData.value.productionLine,
      () => headerData.value.dateStr,
      () => row.productionTimeStart,
      () => row.productionTimeEnd,
      () => row.type
    ],
    () => {
      if (
        headerData.value.productionLine &&
        headerData.value.dateStr &&
        row.productionTimeStart &&
        row.productionTimeEnd &&
        row.type
      ) {
        // checkAbnormalWorkExists(row, index)
         debouncedCheck()
      }
    },
    { deep: true, immediate: true } // 立即执行一次
  )
  
  rowWatchers.value.set(index, stop)
}

// 监听表头字段变化（统一处理）
// watch(
//   () => [headerData.value.productionLine, headerData.value.dateStr],
//   () => {
//     bodyData.value.forEach((row, index) => {
//       checkAbnormalWorkExists(row, index)
//     })
//   },
//   { deep: true }
// )

// 新增表身行
const addBodyRow = () => {
  let lastEndTime = ''
  let lastStrTime = ''
  if (bodyData.value.length > 0) {
    lastEndTime = bodyData.value[bodyData.value.length - 1].productionTimeEnd || ''
    lastStrTime = bodyData.value[bodyData.value.length - 1].productionTimeStart || ''
  }
  const number = headerData.value.assembledNum || 0
  const standardWork = headerData.value.standardWork || 0
  const type=  headerData.value.type|| 0
  const newRow = createEmptyBodyRow(lastEndTime, lastStrTime, number, standardWork,type)
  bodyData.value.push(newRow)  // 直接使用创建的 newRow

    // 为新增行设置监听
  setupRowWatcher(newRow, bodyData.value.length - 1)
}

// 删除行时清理监听
const removeBodyRow = (index: number) => {
    // 清理监听器
  if (rowWatchers.value.has(index)) {
    rowWatchers.value.get(index)!()
    rowWatchers.value.delete(index)
  }
  //最后一行不允许删除
  if (bodyData.value.length === 1) {
    message.error('最后一行不允许删除！')
    return
  }
  bodyData.value.splice(index, 1)
}

/** 打开弹窗 */
const open = async (type: string, batchesId?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (batchesId) {
    formLoading.value = true
    try {
      const data = await DayApi.getDay(batchesId)
      // 分离表头和表身数据
      headerData.value = {
        productionLine: data[0].productionLine,
        teamLeader: data[0].teamLeader,
        dateStr: data[0].dateStr,
        requiredAttendanceNum: data[0].requiredAttendanceNum,
        actualAttendanceNum: data[0].actualAttendanceNum,
        assembledNum: data[0].assembledNum,
        assembledTotal: data[0].assembledTotal,
        standardWork: data[0].standardWork,
        remarkHead: data[0].remarkHead,
        batchesId: data[0].batchesId,
        numberWork: data[0].numberWork,
        type: data[0].type
      }
      // 如果是修改
      bodyData.value = data.map((item) => ({
        id: item.id,
        productionTimeStart: item.productionTime.split('-')[0],
        productionTimeEnd: item.productionTime.split('-')[1],
        productionTime: item.productionTime,
        productionOrderCode: item.productionOrderCode,
        salesOrderCode: item.salesOrderCode,
        productNo: item.productNo,
        modelsOrColor: item.modelsOrColor,
        workOrderNum: item.workOrderNum,
        units: item.units,
        number: item.number,
        hoursReportNum: item.hoursReportNum,
        totalReportNum: item.totalReportNum,
        standardWork: item.standardWork,
        actualWork: item.actualWork,
        abnormalWork: item.abnormalWork,
        abnormalNum: item.abnormalNum,
        abnormalRemark:item.abnormalRemark,
        abnormalCountermeasures:item.abnormalCountermeasures,
        type: item.type,
        audit: item.audit,
        remark: item.remark
      }))

      // 初始化 originalReportNums 和 rowTotalReportNums
      bodyData.value.forEach(row => {
        const key = `${row.productionOrderCode}-${row.type}`
        originalReportNums.value[key] = row.hoursReportNum || 0
        rowTotalReportNums.value[key] = row.totalReportNum || 0
      })
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时，默认添加一行表身数据
    bodyData.value = [createEmptyBodyRow()]
    setupRowWatcher(bodyData.value[0],0)

    //赋值当前登录用户为班组长
    //查询当前用户上一条表头信息
    const data = await DayApi.getPrevious()
    if (data) {
      headerData.value.productionLine = data.productionLine
      headerData.value.dateStr = dateUtil(new Date()).format('YYYY-MM-DD')
        ; (headerData.value.requiredAttendanceNum = data.requiredAttendanceNum),
          (headerData.value.actualAttendanceNum = data.actualAttendanceNum),
          (headerData.value.assembledNum = data.assembledNum),
          (headerData.value.assembledTotal = data.assembledTotal),
          (headerData.value.remarkHead = data.remarkHead),
          (headerData.value.type = data.type)
      syncBodyNumber()
    } else {
      headerData.value.teamLeader = useUserStore().user.nickname
      headerData.value.dateStr = dateUtil(new Date()).format('YYYY-MM-DD')
      headerData.value.type = 1
      headerData.value.productionLine = 1
    }

    headerData.value.teamLeader = useUserStore().user.nickname
    totalReportNum.value = 0;
    //重置批次号
    headerData.value.batchesId = undefined;
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// 处理异常工时表单提交成功事件
const handleAbnormalSuccess = (data) => {
  console.log('接收到异常工时数据:', data)
  const num=data.id
  bodyData.value[num].abnormalNum=data.abnormalNum
  bodyData.value[num].abnormalWork=data.abnormalWork
  bodyData.value[num].abnormalRemark=data.abnormalRemark
  bodyData.value[num].abnormalCountermeasures=data.abnormalCountermeasures
}
//处理出勤工时表单提交成功事件
const handleAttendanceSuccess = (data) => {
  console.log('接收到出勤工时数据:', data)
    
  headerData.value.batchesId = data.batchesId
  headerData.value.numberWork = data.attendanceWorkingHours
}
// Add validation rules for body form
const validateBodyForm = () => {
    // 检查：同一工单号 + 同一工序不能重复添加
  // const uniqueKeys = new Set<string>();
  for (let i = 0; i < bodyData.value.length; i++) {
        const row = bodyData.value[i];
    // const key = `${row.productionOrderCode}-${row.type}`; // 使用组合键判断唯一性

    // if (uniqueKeys.has(key)) {
    //   message.error(`第${i + 1}行：工单号【${row.productionOrderCode}】与工序【${getLabelByType(row.type)}】组合已存在，请勿重复添加`);
    //   return false;
    // }

    // if (row.productionOrderCode && row.type !== undefined) {
    //   uniqueKeys.add(key);
    // }
    if (!row.productionTimeStart || !row.productionTimeEnd) {
      message.error(`第${i + 1}行：请选择时间段`)
      return false
    }
    if (!row.workOrderNum) {
      message.error(`第${i + 1}行：工单数量不能为空，请选择生产工单号或销售订单号`)
      return false
    }
    if (!row.number) {
      message.error(`第${i + 1}行：人数不能为空，请输入出勤人数`)
      return false
    }
    if (!row.hoursReportNum) {
      message.error(`第${i + 1}行：请输入生产数`)
      return false
    }
  }
  return true
}

const getLabelByType = (type: number | string): string => {
  const dict = getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE).find(
    (d) => d.value === type
  );
  return dict?.label || String(type);
};

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  if (bodyData.value.length === 0) {
    message.error('请至少添加一行表身数据')
    return
  }

  // 校验表身数据
  if (!validateBodyForm()) {
    return
  }

  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      // 新增时，将表头数据合并到每一行表身数据中，组成数组一次性提交
      const createData = bodyData.value.map((bodyRow) => ({
        ...headerData.value,
        ...bodyRow,
        productionTime: `${bodyRow.productionTimeStart || ''}-${bodyRow.productionTimeEnd || ''}`,
        type: bodyRow.type
      })) as DayVO[]
      await DayApi.createDay(createData)
      message.success(t('common.createSuccess'))
    } else {
      // 修改时
      await DayApi.batchUpdateDays(
        bodyData.value.map((row) => ({
          ...headerData.value,
          ...row,
          type: row.type,
          productionTime: `${row.productionTimeStart || ''}-${row.productionTimeEnd || ''}`
        }))
      )
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/* 单头组装人数、标准工时填写同步至单身 */
const syncBodyNumber = () => {
  bodyData.value.forEach((row) => {
    row.number = headerData.value.assembledNum
    row.standardWork = headerData.value.standardWork
  })
}
/** 重置表单 */
const resetForm = () => {
  headerData.value = {
    teamLeader: '',
    dateStr: '',
    requiredAttendanceNum: undefined,
    actualAttendanceNum: undefined,
    assembledNum: undefined,
    assembledTotal: undefined,
    remarkHead: undefined,
    standardWork: undefined,
    batchesId: undefined,
    numberWork: undefined,
    type: 1,
    productionLine: 1
  }
  bodyData.value = []
  originalReportNums.value = {}
  rowTotalReportNums.value = {}
  formRef.value?.resetFields()
}

// 存储每个工单的原始生产数值
const originalReportNums = ref<Record<string, number>>({})
// 记录每行的累计数
const rowTotalReportNums = ref<Record<string, number>>({}) 
const validateHoursReportNum = (val: string, row: any) => {
  const numVal = Number(val)

  if (numVal > row.workOrderNum) {
    row.hoursReportNum = row.workOrderNum
    message.warning('小时完成数量不能超过工单数量')
  } else {
    const orderId = `${row.productionOrderCode}-${row.type}`

    const currentOriginal = originalReportNums.value[orderId] || 0
    const change = numVal - currentOriginal

    // 更新该行的累计值
    rowTotalReportNums.value[orderId] = (rowTotalReportNums.value[orderId] || 0) + change

    // 更新原始值记录
    originalReportNums.value[orderId] = numVal

    // 将当前累计值写入 row.totalReportNum
    row.totalReportNum = rowTotalReportNums.value[orderId]
  }
}
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

// 自动完成组件样式
.autocomplete-item {
  .main-text {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .sub-text {
    font-size: 12px;
    color: #909399;
    line-height: 1.2;
  }
}

.no-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 8px 0;
}

.time-range-row {
  display: flex;
  align-items: center;

  .time-select {
    margin: 0 4px;
  }

  .time-range-separator {
    margin: 0 6px;
    font-size: 16px;
    color: #606266;
    line-height: 32px;
  }
}

.card-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  text-align: center;
}

@media (max-width: 768px) {
  .mobile-body-form :deep(.el-form-item__label) {
    text-align: right !important;
    width: 80px !important;
    display: inline-block;
  }
  :deep(.el-form-item__content .el-input-group){
    margin-top: 5px !important;
  }
}

:deep(.el-form-item__label) {
  font-size: 12px !important;
}

:deep(.el-card__body),
:deep(.el-card__header) {
  padding: 2px !important;
}

:deep(.el-scrollbar__thumb) {
  background-color: #000000 !important;
}

:deep(.el-form-item) {
  margin-bottom: 2px !important;
}

:deep(.el-table__cell) {
  padding: 1px 0 !important;

  .cell {
    padding: 1px 0 !important;
  }

  .el-select__wrapper {
    box-shadow: none !important;
  }
}

.custom-card :deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.custom-card) {
  transform: translateY(-17px);
}

:deep(.el-input__prefix) {
  display: none !important;
}

:deep(.el-select__prefix) {
  display: none !important;
}
</style>

<style lang="scss">
// 移动端弹窗样式
@media screen and (max-width: 768px) {
  .el-dialog {
    margin: 0 !important;
    height: 100vh;
    max-width: 100vw;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
  }

  .el-dialog__header {
    padding: 10px;
    margin-right: 0;
  }

  .el-dialog__footer {
    padding: 10px;
    border-top: 1px solid #dcdfe6;
  }

  .el-input__wrapper {
    box-shadow: none !important;
    padding: 0px 0px !important;
  }

  .el-select__wrapper {
    box-shadow: none !important;
  }

  .el-form-item {
    box-shadow: 0 2px 6px rgba(64, 64, 65, 0.1); // 更淡的蓝紫色阴影
    transition: box-shadow 0.2s ease; // 缩短过渡时间
    margin-bottom: 0px !important;
    height: 40px !important;
  }

  .el-form-item__label,
  .el-form-item__content {
    height: 40px !important;
    align-items: center !important;
    line-height: 40px !important;
  }
}

// 全局样式，用于自动完成下拉框
.production-order-autocomplete,
.sales-order-autocomplete {
  .el-autocomplete-suggestion__list {
    max-height: 200px;
    overflow-y: auto;
  }

  .el-autocomplete-suggestion__item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
      background-color: #f5f7fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
