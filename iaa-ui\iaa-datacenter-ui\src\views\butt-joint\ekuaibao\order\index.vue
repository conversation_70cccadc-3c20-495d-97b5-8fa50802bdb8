<template>
  <ContentWrap>
    <x-table
      :data="dataList"
      :total="total"
      :columns="columns"
      :page="queryParams"
      @search="onSerchPageData"
      @pagination="(page) => onSerchPageData(page, true)"
      stripe
      highlight-current
      v-loading="loading"
      height="calc(100vh - 350px)"
      :operation-width="180"
    >
      <template #x-table-header>
        <div class="flex">
          <el-input placeholder="请输入单据编号" size="small" v-model="downloadCode" />
          <el-button type="primary" size="small" @click="downloadOrder">手动下载</el-button>
        </div>
      </template>
      <template #operation="{ row }">
        <el-button type="warning" size="small" link @click="showOrder(row)">详情</el-button>
        <el-button type="primary" size="small" link @click="updateOrder(row)">更新</el-button>
        <el-button type="success" size="small" link @click="syncOrder(row)">同步</el-button>
        <el-button type="danger" size="small" link @click="unSyncOrder(row)">不同步</el-button>
      </template>
    </x-table>
  </ContentWrap>

  <el-drawer title="单据详情" v-model="drawerVisible" size="40%">
    <el-descriptions :column="3" border>
      <el-descriptions-item
        v-for="column in columns"
        :label="column.label"
        :key="column.prop"
        v-show="column.prop !== 'errorMsg'"
      >
        <dict-tag :type="column.dict" :value="detailsRow[column.prop]" v-if="column.dict" />
        <span v-else>{{ detailsRow[column.prop] }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="费用部门" :span="3">{{
        detailsRow.deptName
      }}</el-descriptions-item>
      <el-descriptions-item label="费用明细" :span="3">
        <el-row
          :gutter="10"
          v-for="(item, index) in detailsRow?.feeItems"
          :key="index"
          style="border-bottom: 1px solid #ebeef5"
        >
          <el-col :span="16">{{ item.feeTypeName }}</el-col>
          <el-col :span="8">{{ item.amount }}</el-col>
        </el-row>
      </el-descriptions-item>
      <el-descriptions-item label="核销明细" :span="3">
        <el-row
          :gutter="10"
          v-for="(item, index) in detailsRow?.writeOffItems"
          :key="index"
          style="border-bottom: 1px solid #ebeef5"
        >
          <el-col :span="16">{{ item.writeOffCode }}</el-col>
          <el-col :span="8">{{ item.amount }}</el-col>
        </el-row>
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script lang="ts" setup>
import * as OrderApi from '@/api/butt-joint/ekuaibao/order'
import { cloneDeep } from 'lodash-es'

const drawerVisible = ref(false)
const detailsRow = ref<any>()

const dataList = ref<any[]>([])
const total = ref(0)
const columns = ref<TableColumn[]>([])
const loading = ref(false)
const message = useMessage()
const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})
const downloadCode = ref('')
/** 查询表格数据 */
const onSerchPageData = (row: any, hasPage?: boolean) => {
  for (let key in row) {
    if (row[key]) {
      queryParams.value[key] = row[key]
    } else if (queryParams.value.hasOwnProperty(key)) {
      delete queryParams.value[key]
    }
  }
  if (!hasPage) {
    queryParams.value.pageNo = 1
  }
  onPageData()
}

/** 查询数据 */
const onPageData = async () => {
  loading.value = true
  try {
    let query = cloneDeep(queryParams.value)
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }

    const res = await OrderApi.page(query)
    dataList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 查询列信息及ERP 部门字典 */
const initDeptData = async () => {
  const resColumns = await OrderApi.getTableColumn()
  columns.value = resColumns
}
/** 显示单据详情 */
const showOrder = (row: any) => {
  drawerVisible.value = true
  detailsRow.value = row
}
/** 更新单据 */
const updateOrder = async (row: any) => {
  await message.confirm(`确定要更新单据${row.code}吗？`)
  loading.value = true
  try {
    await OrderApi.updateOrder(row.id)
    onPageData()
  } finally {
    loading.value = false
  }
}

/** 同步单据 */
const syncOrder = async (row: any) => {
  await message.confirm(`确定要手动同步单据${row.code}到ERP吗？`)
  loading.value = true
  try {
    await OrderApi.syncOrder(row.id)
    onPageData()
  } finally {
    loading.value = false
  }
}

const downloadOrder = async () => {
  if (!downloadCode.value) return
  await OrderApi.downloadOrder(downloadCode.value)
  message.success('成功')
  downloadCode.value = ''
  onPageData()
}
/** 不同步单据 */
const unSyncOrder = async (row: any) => {
  await message.confirm(`确定要不同步单据${row.code}到ERP吗？`)
  loading.value = true
  try {
    await OrderApi.unSyncOrder(row.id)
    onPageData()
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  initDeptData()
  onPageData()
})
</script>

<style scoped>
.el-dropdown-link {
  margin-left: 18px;
  margin-top: 4px;
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
</style>
