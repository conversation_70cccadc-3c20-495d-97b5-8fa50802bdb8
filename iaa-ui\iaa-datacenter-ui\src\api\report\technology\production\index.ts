import request from '@/config/axios'

// 生产日报 VO
export interface DayVO {
  id: number // 主键
  batchesId: string // 批次ID
  teamLeader: string // 班组长
  dateStr: Date // 日期
  requiredAttendanceNum: number // 应出勤人数
  actualAttendanceNum: number // 实际出勤人数
  assembledNum: number // 组装人数
  assembledTotal: number // 组装总工时
  productionTime: string // 生产时间段
  productionOrderCode: string // 生产工单号
  salesOrderCode: string // 销售订单号
  productNo: string // 品号
  modelsOrColor: string // 机型/颜色(品名)
  workOrderNum: number // 工单数量(销售订单数量)
  units: string // 单位
  number: number // 人数
  hoursReportNum: number // 小时完成数量
  totalReportNum: number // 累计完成数量
  standardWork: number // 标准工时
  actualWork: number // 实际工时
  avgWork: number // 平均工时
  productionLine : number // 生产线
  type: number // 类型：0 包装、1 组装
  audit: number // 是否已审核：0 未审核、1 已审核(已审核的不能再修改)
  abnormalWork: number// 异常工时
  abnormalNum: number// 异常数量
  abnormalRemark: string// 异常问题点
  abnormalCountermeasures: string// 异常对策 
  remark: string // 备注
  remarkHead: string // 单头备注
}

//异常工时 VO
export interface AbnormalDayVO {
  id: number // 主键
  batchesId: string // 批次ID
  productionTime: string // 录入时间段
  abnormalWorkingHours: number // 异常工时
  number: number // 人数
  remark: string // 备注
}

// 生产日报 API
export const DayApi = {
  // 查询生产日报分页
  getDayPage: async (data: any) => {
    return await request.post({ url: `/report/production-day/page`, data })
  },

  // 根据批次ID查询生产日报详情
  getDay: async (batchesId: any) => {
    return await request.get({ url: `/report/production-day/get?batchesId=` + batchesId })
  },

  getOrderCodeNum: async (data: DayVO) => {
    return await request.post({ url: `/report/production-day/getOrderCodeNum`, data })
  },
  // 新增生产日报
  createDay: async (data: DayVO[]) => {
    return await request.post({ url: `/report/production-day/add`, data })
  },

  // 新增异常工时
  createAbnormalDay: async (data: any[]) => {
    return await request.post({ url: `/report/production-day/addAbnormal`, data })
  },
  //删除异常工时
  deleteAbnormalDay: async (batchesId: any) => {
    return await request.get({ url: `/report/production-day/deleteAbnormal/${batchesId}`})
  },

  // 根据批次ID查询异常工时
  getAbnormalDay: async (batchesId: any) => {
    return await request.get({ url: `/report/production-day/getAbnormal/${batchesId}`}) 
  },
  // 根据批次ID查询出勤工时
  getAttendanceDay: async (batchesId: any) => {
    return await request.get({ url: `/report/production-day/getAttendance/${batchesId}`}) 
  },
    // 新增出勤工时
  createAttendance: async (data: any) => {
    return await request.post({ url: `/report/production-day/addAttendance`, data })
  },
  //删除出勤工时
  deleteAttendanceDay: async (batchesId: any) => {
     return await request.get({ url: `/report/production-day/deleteAttendance/${batchesId}`})
  },
  // 修改生产日报
  updateDay: async (data: DayVO) => {
    return await request.post({ url: `/report/production-day/update`, data })
  },

  //批量修改生产日报
  batchUpdateDays: async (data: DayVO[]) => {
    return await request.post({ url: `/report/production-day/batch-update`, data })
  },

  //批量修改异常人数
  batchUpdateAbnormalNum: async (data: any[]) => {
    return await request.post({ url: `/report/production-day/batch-update-abnormal-num`, data })
  },

  // 删除生产日报
  deleteDay: async (id: number) => {
    return await request.get({ url: `/report/production-day/delete/${id}`})
  },

  // 批量删除生产日报
  deleteDays: async (data:number[]) => {
    return await request.post({ url: `/report/production-day/delete/ids`,data})
  },

  getPrevious: async () => {
    return await request.get({ url: `/report/production-day/getPrevious`})
  },
  auditDay: async () => {
    return await request.get({ url: `/report/production-day/auditDay/all`})
  },

  // 批量审批生产日报
  auditDays: async (data:number[]) => {
    return await request.post({ url: `/report/production-day/auditDay/ids`,data})
  },

  // 导出生产日报 Excel
  exportDay: async (params) => {
    return await request.download({ url: `/report/production-day/export-excel`, params })
  },

  // 根据工单号或销售订单号查询选项
  getWorkSalesOptions: async (docNo: string, type: number) => {
    return await request.get({
      url: `/report/production-day/work-sales`,
      params: { docNo, type }
    })
  },

  // 根据品号和工单号和销售订单号查询选项
  getProductNo: async (data:any) => {
      return await request.post({ url: `/report/production-day/getProductNo`, data })
  },
}
