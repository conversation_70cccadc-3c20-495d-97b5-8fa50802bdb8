<template>
  <ContentWrap>
    <div class="pc-view" v-if="!mobile">
      <vxe-toolbar custom ref="toolbarRef" size="mini">
        <template #buttons>
          <el-button
            type="primary"
            size="small"
            @click="openForm('create')"
            v-hasPermi="['production:day:create']"
          >
            新增
          </el-button>
          <el-button
            @click="handleAudit()"
            type="success"
            v-hasPermi="['production:day:audit']"
            style="margin-left: 30px"
            size="small"
            >审核</el-button
          >
          <el-button
            @click="handleExport()"
            type="warning"
            v-hasPermi="['production:day:export']"
            style="margin-left: 30px"
            size="small"
            >导出</el-button
          >
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100vh-260px)]">
        <vxe-table
          :row-config="{ height: 30 }"
          ref="tableRef"
          @scroll="handleScroll"
          @rendered="handleRendered"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          @cell-dblclick="cellClickEvent"
          :filter-config="{ showIcon: false }"
          border
          stripe
          align="center"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{enabled: true, gt: 0}"
          :loading="loading"
          :menu-config="menuConfig"
          @menu-click="menuClickEvent"
          :checkbox-config="{ labelField: 'name', highlight: true, range: true }"
          :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
          height="100%"
          max-height="100%"
          :footer-cell-config="{ height: 30 }"
          :footer-data="footerData"
          show-footer
          :footer-cell-style="{ padding: 0, background: '#dcefdc', border: '1px solid #ebeef5' }"
        >
          <vxe-column type="checkbox" width="60" field="id" />
          <vxe-column field="productionLine" width="100">
            <template #header>
              <div>产线</div>
              <el-select
                v-model="queryParams.productionLine"
                @change="handleList"
                placeholder="选择产线"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_LINE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              {{ formattedProductionLine(row.productionLine) }}
            </template>
          </vxe-column>
          <vxe-column field="dateStr" width="180">
            <template #header>
              <div style="display: flex; flex-direction: column; gap: 8px">
                <el-date-picker
                  v-model="queryParams.startDate"
                  @change="handleList"
                  type="date"
                  placeholder="开始日期"
                  size="small"
                  clearable
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
                <el-date-picker
                  v-model="queryParams.endDate"
                  @change="handleList"
                  type="date"
                  placeholder="结束日期"
                  size="small"
                  clearable
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </div>
            </template>
            <template #default="{ row }">
              {{ row.dateStr && formatToDate(row.dateStr) }}
            </template>
          </vxe-column>
          <vxe-column title="时间段" width="120" field="productionTime">
            <template #default="{ row }">
              {{ row.productionTime && formatTime(row.productionTime) }}
            </template>
          </vxe-column>
          <!-- 支持查询的字段 - 班组长 -->

          <!-- 支持查询的字段 - 生产工单号 -->
          <vxe-column field="productionOrderCode" width="150">
            <template #header>
              <div>生产工单号</div>
              <el-input
                v-model="queryParams.productionOrderCode"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>

          <!-- 支持查询的字段 - 销售订单号 -->
          <vxe-column field="salesOrderCode" width="150">
            <template #header>
              <div>销售订单号</div>
              <el-input
                v-model="queryParams.salesOrderCode"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>

          <!-- 支持查询的字段 - 品号 -->
          <vxe-column field="productNo" width="120">
            <template #header>
              <div>品号</div>
              <el-input
                v-model="queryParams.productNo"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="机型/颜色(品名)" width="160" field="modelsOrColor" />
          <vxe-column title="工单数量" width="120" field="workOrderNum" />
          <vxe-column title="单位" width="80" field="units" />
          <vxe-column title="生产数" width="120" field="hoursReportNum" />
          <vxe-column title="累计完成数量" width="120" field="totalReportNum" />
          <!-- <vxe-column title="标准工时" width="100" field="standardWork" /> -->
          <vxe-column title="实际工时" width="100" field="actualWork" />
          <vxe-column title="平均工时" width="100" field="avgWork" />
          <vxe-column title="组装工时" width="100" field="assembledTotal" />
          <vxe-column title="备注/异常原因及对策" width="140" field="remark" />
          <vxe-column title="单头备注" width="120" field="remarkHead" />
          <vxe-column field="number" width="100">
            <template #header>
              <div>行生产人数</div>
              <el-input
                v-model.number="queryParams.number"
                @change="handleList"
                clearable
                placeholder="输入人数"
                style="width: 100%"
                size="small"
                type="number"
                :min="0"
              />
            </template>
          </vxe-column>

          <!-- 支持查询的字段 -工序 -->
          <vxe-column field="type" width="100">
            <template #header>
              <div>工序</div>
              <el-select
                v-model="queryParams.type"
                @change="handleList"
                placeholder="选择工序"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.PRODUCTION_REPORT_TYPE" :value="row.type" />
            </template>
          </vxe-column>
          <vxe-column title="审核状态" width="120" field="assembledTotal">
            <template #default="{ row }">
              <el-tag :type="row.audit === 0 ? 'danger' : 'success'">
                {{ row.audit === 0 ? '未审核' : '已审核' }}
              </el-tag>
            </template>
          </vxe-column>
          <!-- 不支持查询的字段 - 只显示 -->
          <vxe-column title="应出勤人数" width="120" field="requiredAttendanceNum" />
          <vxe-column title="实际出勤人数" width="120" field="actualAttendanceNum" />
          <vxe-column title="组装人数" width="120" field="assembledNum" />
          <vxe-column title="异常人数" width="120" field="abnormalNum" />
          <vxe-column title="异常工时" width="120" field="abnormalWork" />
          <vxe-column title="异常问题点" width="120" field="abnormalRemark" />
          <vxe-column title="异常对策" width="120" field="abnormalCountermeasures" />
          <!-- <vxe-column title="组装总工时" width="120" field="assembledTotal" /> -->

          <vxe-column title="最后活动时间" width="160" field="createTime">
            <template #default="{ row }">
              {{ row.createTime && formatToDateTime(row.createTime) }}
            </template>
          </vxe-column>
          <vxe-column field="teamLeader" width="120">
            <template #header>
              <div>班组长</div>
              <el-input
                v-model="queryParams.teamLeader"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="操作" width="160" fixed="right">
            <template #default="{ row }">
              <div v-if="row.audit === 0">
                <el-button
                  @click="handleDelete(row.id)"
                  link
                  type="danger"
                  v-hasPermi="['production:day:delete']"
                  >删除</el-button
                >
              </div>
              <div v-if="row.audit === 1">
                <el-button
                  @click="handleDelete(row.id)"
                  link
                  type="danger"
                  v-hasPermi="['production:day:auditdelete']"
                  >删除</el-button
                >
              </div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 移动端视图 -->
    <div class="h-[calc(100vh-110px)]" v-else v-loading="viewSwitching">
      <div class="h-40px flex gap-2 mb-2">
        <el-input
          v-model="queryParams.all"
          size="large"
          :suffix-icon="Search"
          placeholder="全域查询"
          clearable
          @change="handleList"
          class="flex-1"
        />
        <el-button
          type="primary"
          @click="openForm('create')"
          size="large"
          v-hasPermi="['production:day:create']"
        >
          <Icon icon="ep:plus" />
        </el-button>
        <el-button type="success" @click="handleAudit()" size="large">
          <Icon icon="ep:check" />
        </el-button>
        <el-button type="info" @click="toggleViewMode" size="large">
          <Icon :icon="isTableView ? 'ep:list' : 'ep:grid'" />
        </el-button>
      </div>

      <!-- 卡片视图 -->
      <div
        v-if="!isTableView"
        class="h-[calc(100%-75px)] overflow-auto"
        v-infinite-scroll="load"
        :infinite-scroll-distance="20"
        :infinite-scroll-immediate="disabled"
        v-loading="loading"
      >
        <div v-for="item in list" :key="item.id" class="p-10px data-item position-relative mb-2">
          <div class="card-title"
            >班组长：{{ item.teamLeader }} | 日期：{{
              item.dateStr && formatToDate(item.dateStr)
            }}</div
          >
          <el-form size="small">
            <el-row>
              <el-col :span="24">
                <el-form-item label="产线：">{{
                  formattedProductionLine(item.productionLine)
                }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="应出勤">{{ item.requiredAttendanceNum || 0 }}人</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实际出勤">{{ item.actualAttendanceNum || 0 }}人</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="组装人数">{{ item.assembledNum || 0 }}人</el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="组装总工时">{{ item.assembledTotal || 0 }}小时</el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="时间段">{{
                  item.productionTime && formatTime(item.productionTime)
                }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="单头备注">{{ item.remarkHead }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="生产工单号">{{ item.productionOrderCode }}</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="销售订单号">{{ item.salesOrderCode }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="品号">{{ item.productNo }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="品名">{{ item.modelsOrColor }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="数量">{{ item.workOrderNum }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单位">{{ item.units }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="人数">{{ item.number || 0 }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="生产数">{{ item.hoursReportNum }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="累计完成">{{ item.totalReportNum }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标准工时">{{ item.standardWork }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="实际工时">{{ item.actualWork }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工序">
                  <template #default>
                    <dict-tag :type="DICT_TYPE.PRODUCTION_REPORT_TYPE" :value="item.type" />
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="审核状态">
                  <el-tag :type="item.audit === 0 ? 'danger' : 'success'">
                    {{ item.audit === 0 ? '未审核' : '已审核' }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单身备注">{{ item.remark }}</el-form-item>
              </el-col>
              <el-col :span="24" v-if="item.abnormalWork">
                <el-form-item label="异常工时">{{ item.abnormalWork }}</el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <!-- 操作按钮区域 -->
          <div class="mt-2">
            <template v-if="item.audit === 0">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-button
                    type="primary"
                    plain
                    @click="openForm('update', item.batchesId)"
                    class="w-full"
                    v-hasPermi="['production:day:update']"
                    >编辑</el-button
                  >
                </el-col>
                <el-col :span="12">
                  <el-button
                    type="danger"
                    plain
                    @click="handleDelete(item.id)"
                    class="w-full"
                    v-hasPermi="['production:day:delete']"
                    >删除</el-button
                  >
                </el-col>
              </el-row>
            </template>
            <template v-if="item.audit === 1">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-button
                    type="primary"
                    plain
                    @click="openForm('update', item.batchesId)"
                    class="w-full"
                    v-hasPermi="['production:day:auditupdate']"
                    >编辑</el-button
                  >
                </el-col>
                <el-col :span="12">
                  <el-button
                    type="danger"
                    plain
                    @click="handleDelete(item.id)"
                    class="w-full"
                    v-hasPermi="['production:day:auditdelete']"
                    >删除</el-button
                  >
                </el-col>
              </el-row>
            </template>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-else class="table-view-wrapper">
        <div class="table-scroll">
          <vxe-table
            @cell-dblclick="cellClickEvent"
            ref="tableRef"
            :row-config="{ height: 30, isCurrent: true }"
            :data="list"
            :header-cell-style="{ padding: 0 }"
            :cell-style="{ padding: 0, height: '30px', color: '#232323' }"
            :virtual-y-config="{enabled: true, gt: 0}"
            border
            stripe
            show-overflow
            align="center"
            height="85%"
          >
            <vxe-column field="productionLine" width="100" title="产线">
              <template #header>
                <div>产线</div>
                <el-select
                  v-model="queryParams.productionLine"
                  @change="handleList"
                  placeholder="选择产线"
                  style="width: 100%"
                  size="small"
                  clearable
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_LINE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
              <template #default="{ row }">
                {{ formattedProductionLine(row.productionLine) }}
              </template>
            </vxe-column>
            <vxe-column field="dateStr" width="100" title="日期">
              <template #header>
                <div>日期</div>
                <el-date-picker
                  v-model="queryParams.dateStr"
                  @change="handleList"
                  type="date"
                  placeholder="开始日期"
                  size="small"
                  clearable
                  value-format="YYYY-MM-DD"
                  class="!w-100%"
                />
              </template>
              <template #default="{ row }">
                {{ row.dateStr && formatToDate(row.dateStr) }}
              </template>
            </vxe-column>
            <vxe-column field="productionTime" width="100" title="时间段" />
            <vxe-column field="teamLeader" width="80" title="班组长" />
            <vxe-column field="productionOrderCode" width="150" title="生产工单号" />
            <vxe-column field="salesOrderCode" width="150" title="销售订单号" />
            <vxe-column field="productNo" width="100" title="品号" />
            <vxe-column field="modelsOrColor" width="120" title="机型/颜色" />
            <vxe-column field="workOrderNum" width="80" title="工单数量" />
            <vxe-column field="units" width="80" title="单位" />
            <vxe-column field="number" width="80" title="人数" />
            <vxe-column field="type" width="80" title="工序">
              <template #default="{ row }">
                <dict-tag :type="DICT_TYPE.PRODUCTION_REPORT_TYPE" :value="row.type" />
              </template>
            </vxe-column>
            <vxe-column field="hoursReportNum" width="80" title="小时完成" />
            <vxe-column field="totalReportNum" width="80" title="累计完成" />
            <vxe-column field="avgWork" width="80" title="平均工时" />
            <vxe-column field="actualWork" width="80" title="实际工时" />
            <vxe-column field="standardWork" width="80" title="标准工时" />
            <vxe-column field="remark" width="180" title="备注" />
            <vxe-column title="异常人数" width="120" field="abnormalNum" />
            <vxe-column title="异常工时" width="120" field="abnormalWork" />
            <vxe-column title="异常问题点" width="120" field="abnormalRemark" />
            <vxe-column title="异常对策" width="120" field="abnormalCountermeasures" />
            <vxe-column title="操作" width="140">
              <template #default="{ row }">
                <div v-if="row.audit === 0">
                  <el-button
                    @click="openForm('update', row.batchesId)"
                    link
                    v-hasPermi="['production:day:update']"
                    >编辑</el-button
                  >
                  <el-button
                    @click="handleDelete(row.id)"
                    link
                    type="danger"
                    v-hasPermi="['production:day:delete']"
                    >删除</el-button
                  >
                </div>
                <div v-if="row.audit === 1">
                  <el-button
                    @click="openForm('update', row.batchesId)"
                    link
                    v-hasPermi="['production:day:auditupdate']"
                    >编辑</el-button
                  >
                  <el-button
                    @click="handleDelete(row.id)"
                    link
                    type="danger"
                    v-hasPermi="['production:day:auditdelete']"
                    >删除</el-button
                  >
                </div>
              </template>
            </vxe-column>
          </vxe-table>
          <div style="margin-top: 10px">
            <el-pagination
              v-model:current-page="queryParams.pageNo"
              v-model:page-size="queryParams.pageSize"
              :page-sizes="[10, 20]"
              :total="total"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              small
            />
          </div>
        </div>
      </div>
      <div class="h-30px leading-30px border-t-#a8a8a8">共计：{{ total }}条记录</div>
    </div>
    <!-- 批量删除确认对话框 -->
    <Dialog title="批量删除确认" v-model="batchDeleteVisible">
      <div>当前选中：{{ selectionData.length }} 条数据，确认要删除吗？</div>
      <template #footer>
        <el-button type="danger" @click="confirmBatchDelete()">确认删除</el-button>
        <el-button @click="batchDeleteVisible = false">取消</el-button>
      </template>
    </Dialog>

    <!-- 批量审核确认对话框 -->
    <Dialog title="批量审核确认" v-model="batchAuditVisible">
      <div
        >当前选中：{{
          selectionData.length
        }}
        条数据，确认要批量审核通过吗？(审核通过后，数据将无法修改！)</div
      >
      <template #footer>
        <el-button type="danger" @click="confirmBatchAudit()">确认通过</el-button>
        <el-button @click="batchDeleteVisible = false">取消</el-button>
      </template>
    </Dialog>

    <!-- 表单弹窗：添加/修改 -->
    <DayForm ref="formRef" @success="getList(true)" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { formatToDateTime, formatToDate } from '@/utils/dateUtil'
import { DayApi, DayVO } from '@/api/report/technology/production'
import DayForm from './DayForm.vue'
import { cloneDeep, last } from 'lodash-es'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import download from '@/utils/download'
import { useAppStore } from '@/store/modules/app'
import { Search } from '@element-plus/icons-vue'
import { checkPermission } from '@/store/modules/permission'
import type { VxeTablePropTypes } from 'vxe-table'
import { ref } from 'vue';
/** 生产日报 列表 */
defineOptions({ name: 'Day' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const appStore = useAppStore()

const tableRef = ref()
const toolbarRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref<DayVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

// 滚动位置管理
const TABLE_SCROLL_KEY = 'productionDayTableScrollPosition'
const savedScrollPosition = ref({ scrollTop: 0, scrollLeft: 0 })
const shouldRestoreScroll = ref(false)

// 保存滚动位置
const handleScroll = (params: any) => {
  const { scrollTop, scrollLeft } = params

  // 实时保存滚动位置
  savedScrollPosition.value = { scrollTop, scrollLeft }

  // 同时保存到localStorage作为备份
  localStorage.setItem(TABLE_SCROLL_KEY, JSON.stringify({ scrollTop, scrollLeft }))

  console.log('保存滚动位置:', { scrollTop, scrollLeft })
}

// 恢复滚动位置
const handleRendered = () => {
  // 只有在需要恢复滚动位置时才执行
  if (shouldRestoreScroll.value && tableRef.value) {
    nextTick(() => {
      const { scrollTop, scrollLeft } = savedScrollPosition.value
      console.log('恢复滚动位置:', { scrollTop, scrollLeft })

      // 使用setTimeout确保DOM完全渲染后再滚动
      setTimeout(() => {
        if (tableRef.value) {
          tableRef.value.scrollTo({ top: scrollTop, left: scrollLeft })
          shouldRestoreScroll.value = false // 恢复后重置标志
        }
      }, 50)
    })
  }
}

// 初始化滚动位置（从localStorage恢复）
const initScrollPosition = () => {
  try {
    const saved = localStorage.getItem(TABLE_SCROLL_KEY)
    if (saved) {
      const position = JSON.parse(saved)
      savedScrollPosition.value = position
      console.log('从localStorage恢复滚动位置:', position)
    }
  } catch (error) {
    console.warn('恢复滚动位置失败:', error)
  }
}
// 移动端相关
const mobile = computed(() => appStore.getMobile)
const disabled = ref(false)

// 批量删除相关
const batchDeleteVisible = ref(false)
const selectionData = ref<any[]>([])

// 批量审核相关
const batchAuditVisible = ref(false)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  // 支持查询的字段
  teamLeader: undefined,
  productionOrderCode: undefined,
  salesOrderCode: undefined,
  productNo: undefined,
  dateStr: '',
  startDate: undefined, // 新增：开始日期
  endDate: undefined, // 新增：结束日期
  type: undefined,
  productionLine: undefined,
  number: undefined,
  // 移动端全域搜索
  all: undefined
})
const queryFormRef = ref() // 搜索的表单
const menuConfig = reactive<any>({
  body: {
    options: [[{ code: 'batchDelete', name: '批量删除' }]]
  }
})

const productionLineLabels = {
  1: 'L1',
  2: 'L2',
  3: 'L3',
  4: 'L4',
  5: 'L5',
  6: 'L6',
  7: 'L7',
  8: 'L8',
  9: 'L9',
  10: 'L10',
  11: 'L11',
  12: '小包装线',
  13: '包装1线',
  14: '包装2线'
}
// 计算属性：将数字映射为中文或 Lx 格式
const formattedProductionLine = computed(() => {
  return (value: number) => productionLineLabels[value] || value
})
// 右键菜单点击事件
const menuClickEvent = ({ menu }) => {
  const $table = tableRef.value
  if (!$table) return
  switch (menu.code) {
    case 'batchDelete':
      const rows = $table.getCheckboxRecords()
      if (rows.length === 0) {
        message.alertError('请选择要删除的数据')
        selectionData.value = []
        return
      }
      selectionData.value = rows
      batchDeleteVisible.value = true
      break
  }
}

//实际工时
const totalActualWork = ref()

//生产数
const totalWorkNum = ref(0)

const footerData = ref<VxeTablePropTypes.FooterData>([
  { productionLine: '合计总和', actualWork: totalActualWork, hoursReportNum: totalWorkNum }
])
//双击编辑
const cellClickEvent: any = ({ row, column }) => {
  if (column.title === '操作') return
  // 判断是否是已审核的数据
  if (row.audit === 1) {
    if (!checkPermission(['production:day:auditupdate'])) {
      message.alertError('您没有权限操作已审核数据')
      return
    }
  }

  openForm('update', row.batchesId)
}

// 筛选处理
const handleList = () => {
  queryParams.pageNo = 1
  // 筛选时保持滚动位置
  getList(true)
}

// 移动端无限滚动加载
const load = async () => {
  loading.value = true
  try {
    const maxSize = Math.ceil(total.value / queryParams.pageSize)
    if (queryParams.pageNo >= maxSize) {
      disabled.value = true
      return
    }
    queryParams.pageNo += 1
    let query = cloneDeep(queryParams)
    // 处理查询参数
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const data = await DayApi.getDayPage(query)
    list.value = list.value.concat(data.list)
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 时间格式化（用于显示具体时间点）
const formatTime = (timeValue: any) => {
  if (!timeValue) return ''
  // 如果是时间戳，转换为时间格式
  if (typeof timeValue === 'number') {
    const date = new Date(timeValue)
    return `${date.getHours().toString().padStart(2, '0')}:${date
      .getMinutes()
      .toString()
      .padStart(2, '0')}`
  }
  // 如果已经是时间格式，直接返回
  return timeValue
}

/** 查询列表 */
const getList = async (preserveScroll = false) => {
  loading.value = true

  // 如果需要保持滚动位置，设置恢复标志
  if (preserveScroll) {
    shouldRestoreScroll.value = true
  }

  try {
    let query = cloneDeep(queryParams)
    // 处理查询参数
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const data = await DayApi.getDayPage(query)
    list.value = data.list
    total.value = data.total
    totalActualWork.value = list.value
      .reduce((total, item) => total + item.actualWork, 0)
      .toFixed(3)
    totalWorkNum.value = list.value.reduce((total, item) => total + item.hoursReportNum, 0)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const disabledDate = (time: Date) => {
  if (!queryParams.startDate) return false
  return time.getTime() < new Date(queryParams.startDate).setHours(0, 0, 0, 0)
}
// 批量删除确认
const confirmBatchDelete = async () => {
  loading.value = true
  try {
    let isAudit = true
    selectionData.value.map((item) => {
      if (item.audit === 1) {
        //判断是否有操作已审核数据的权限
        if (!checkPermission(['production:day:auditupdate'])) {
          message.error('选中的数据中包含已审核数据，请重新选择！')
          isAudit = false
          return
        }
      }
    })
    if (isAudit) {
      const ids = selectionData.value.map((item) => item.id)
      await DayApi.deleteDays(ids)
      message.success('批量删除成功')
      batchDeleteVisible.value = false
      selectionData.value = []
    }
    // 删除后保持滚动位置
    getList(true)
  } catch {
    message.error('批量删除失败')
  } finally {
    loading.value = false
  }
}

// 批量审核确认
const confirmBatchAudit = async () => {
  loading.value = true
  try {
    const ids = selectionData.value.map((item) => item.id)
    await DayApi.auditDays(ids)
    message.success('批量审核通过成功')
    batchAuditVisible.value = false
    selectionData.value = []
    // 审核后保持滚动位置
    getList(true)
  } catch {
    message.error('批量审核失败')
  } finally {
    loading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, batchesId?: any) => {
  formRef.value.open(type, batchesId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DayApi.deleteDay(id)
    message.success(t('common.delSuccess'))
    // 刷新列表并保持滚动位置
    await getList(true)
  } catch {}
}

/** 审核按钮操作 */
const handleAudit = async () => {
  // 审核的二次确认
  await message.confirm('确认要审核通过吗？(审核通过后，数据将无法修改！)')
  // 发起审核
  await DayApi.auditDay()
  message.success('审核成功')
  // 刷新列表并保持滚动位置
  await getList(true)
}

// 视图模式控制
const isTableView = ref(false)

const viewSwitching = ref(false)

// 切换视图模式
const toggleViewMode = async () => {
  viewSwitching.value = true
  isTableView.value = !isTableView.value
  queryParams.pageNo = 1
  await getList()
  setTimeout(() => {
    viewSwitching.value = false
  }, 300)
}

const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DayApi.exportDay(queryParams)
    download.excel(data, '生产日报.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 处理每页条数变化
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  queryParams.pageNo = 1
  // 改变每页条数时不保持滚动位置（因为数据结构变化）
  getList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  // 翻页时不保持滚动位置（用户主动翻页）
  getList()
}

/** 初始化 **/
onMounted(() => {
  // 初始化滚动位置
  initScrollPosition()

  getList()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>

<style lang="scss" scoped>
:deep(.vxe-header--column .vxe-cell) {
  padding: 0 !important;
}

:deep(.row--stripe) {
  background-color: #f9f9f9 !important;
}

:deep(.vxe-cell--edit-icon) {
  display: none !important;
}

@media (max-width: 768px) {
  :deep(.el-card__body) {
    padding: 0 !important;
  }

  .data-item {
    padding-right: 30px;
    // 弱化阴影参数
    box-shadow: 0 2px 6px rgba(0, 85, 255, 0.1); // 更淡的蓝紫色阴影
    transition: box-shadow 0.2s ease; // 缩短过渡时间

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
      padding: 8px 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px;
      text-align: center;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 5px;
  }

  :deep(.el-form-item__label) {
    color: #a8a8a8 !important;
  }

  :deep(.el-form-item__content) {
    display: block;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.table-view-wrapper {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 110px);
  min-height: 0;
  background: #fff;
  position: relative; // 关键
}

.table-scroll {
  flex: 1 1 0%;
  min-height: 0;
  overflow-y: auto;
  overflow-x: auto;
}

.table-total {
  color: #888;
  font-size: 14px;
}

:deep(.vxe-table--render-default .vxe-body--row.row--current) {
  background-color: #e6f7ff !important;
}
</style>
